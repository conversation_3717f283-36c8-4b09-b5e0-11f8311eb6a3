# 🧩 Phase 6.5: Advanced Feature Extraction

**Status**: 📋 Planned  
**Estimated Duration**: 3 days  
**Dependencies**: [Phase 6: Note Candidate Window Detection](phase_06_note_candidates.md)  
**Next Phase**: [Phase 7: Basic Note Type Classification](phase_07_note_classification.md)

---

## 1. **Phase Purpose**

This phase extracts comprehensive machine learning features from OGG audio files and note candidate windows to prepare high-quality inputs for the classification models. This step is isolated because:

- **Feature complexity** requires specialized audio processing expertise and tools
- **ML preparation** involves different techniques than basic audio analysis
- **Format optimization** focuses on creating optimal inputs for neural networks
- **Performance tuning** requires RTX 3070 GPU optimization for large-scale feature extraction

**Why Isolated**: Advanced feature extraction involves sophisticated signal processing techniques that are distinct from basic candidate detection. This phase transforms raw audio candidates into ML-ready feature vectors optimized for the classification pipeline.

---

## 2. **Data Flow Specification**

### **Input Data Sources & Dependencies**
- **Primary Source**: Phase 6 outputs (note candidate windows)
- **Secondary Source**: Phase 1 outputs (standardized audio data)
- **Cross-Reference**: Phase 4 outputs (beat positions for context features)

### **Input Data Format**
```python
# From Phase 6: Note Candidate Windows (exact format match)
note_candidates: List[Dict] = [
    {
        "window_id": int,               # Unique window identifier
        "start_time": float,            # Window start time (seconds)
        "end_time": float,              # Window end time (seconds)
        "center_time": float,           # Window center time
        "duration": float,              # Window duration
        "beat_position": float,         # Position relative to nearest beat (0-1)
        "beat_subdivision": str,        # "quarter", "eighth", "sixteenth", etc.
        "onset_strength": float,        # Maximum onset strength in window
        "energy_profile": np.ndarray,   # Energy profile within window
        "spectral_features": Dict,      # Basic spectral characteristics
        "candidate_confidence": float,  # Likelihood of containing a note
        "candidate_type": str,          # "strong", "weak", "subdivision"
        "audio_snippet": np.ndarray     # Short audio excerpt (shape: [n_samples])
    }
]

# From Phase 1: Standardized audio data
audio_data: np.ndarray              # Full audio array (mono, 22050Hz)
sample_rate: int = 22050            # Standardized sample rate

# Input Directory Structure (Windows-compatible paths)
data\\processed\\phase06\\
├── note_candidates\\               # Candidate windows per segment
│   ├── {song_name}_candidates.json # List of note_candidates Dicts
│   └── ...
├── candidate_audio\\               # Audio snippets for candidates
│   ├── {song_name}_{segment}_{window}.npy # Individual audio snippets
│   └── ...
├── candidate_features\\            # Basic extracted features per candidate
│   ├── {song_name}_features.json  # Basic features per candidate
│   └── ...
├── detection_stats\\               # Detection statistics per song
│   ├── {song_name}_stats.json     # Detection statistics
│   └── ...
└── candidate_detection_report.json # Overall detection summary
```

### **Output Data Format (Phase 7 Compatible)**
```python
# Advanced ML-ready features (exact format for Phase 7 consumption)
advanced_features: List[Dict] = [
    {
        "candidate_id": int,            # Matching candidate ID from Phase 6
        "window_id": int,               # Original window identifier
        "feature_vector": np.ndarray,   # Combined feature vector (shape: [128,])
        "spectral_features": {
            "mel_spectrogram": np.ndarray,      # Mel-scale spectrogram (shape: [80, 32])
            "mfcc": np.ndarray,                 # MFCC coefficients (shape: [13,])
            "spectral_centroid": float,         # Spectral centroid (Hz)
            "spectral_rolloff": float,          # Spectral rolloff (Hz)
            "spectral_bandwidth": float,        # Spectral bandwidth (Hz)
            "zero_crossing_rate": float,        # Zero crossing rate (0-1)
            "chroma": np.ndarray,               # Chroma features (shape: [12,])
            "tonnetz": np.ndarray               # Tonal centroid features (shape: [6,])
        },
        "rhythmic_features": {
            "tempo_estimate": float,            # Local tempo estimate (BPM)
            "beat_strength": float,             # Beat tracking strength (0-1)
            "rhythm_pattern": np.ndarray,       # Rhythm pattern vector (shape: [16,])
            "onset_density": float,             # Onsets per second
            "rhythmic_regularity": float       # Rhythmic consistency measure (0-1)
        },
        "harmonic_features": {
            "pitch_estimate": float,            # Fundamental frequency (Hz)
            "pitch_confidence": float,          # Pitch detection confidence (0-1)
            "harmonic_ratio": float,            # Harmonic vs noise ratio (0-1)
            "inharmonicity": float,             # Inharmonicity measure (0-1)
            "pitch_stability": float           # Pitch stability over time (0-1)
        },
        "energy_features": {
            "rms_energy": float,                # RMS energy level (dB)
            "energy_variance": float,           # Energy variation (dB)
            "attack_time": float,               # Attack time (ms)
            "decay_time": float,                # Decay time (ms)
            "sustain_level": float,             # Sustain energy level (dB)
            "dynamic_range": float              # Dynamic range (dB)
        },
        "contextual_features": {
            "preceding_context": np.ndarray,    # Features from preceding window (shape: [64,])
            "following_context": np.ndarray,    # Features from following window (shape: [64,])
            "beat_context": np.ndarray,         # Beat-aligned context features (shape: [32,])
            "measure_position": float,          # Position within measure (0-1)
            "phrase_position": float            # Position within phrase (0-1)
        },
        "quality_metrics": {
            "feature_completeness": float,      # Percentage of features extracted (0-1)
            "extraction_confidence": float,    # Overall extraction confidence (0-1)
            "noise_level": float,               # Estimated noise level (dB)
            "signal_quality": float             # Signal quality assessment (0-1)
        }
    }
]

# Feature extraction statistics
extraction_stats: Dict = {
    "total_candidates_processed": int,      # Total candidates processed
    "successful_extractions": int,          # Successful feature extractions
    "extraction_success_rate": float,      # Success rate percentage
    "average_processing_time_ms": float,   # Average processing time per candidate
    "feature_vector_dimensions": int,      # Final feature vector size
    "gpu_memory_usage_mb": float,          # Peak GPU memory usage
    "quality_distribution": {
        "high_quality": int,                # High-quality extractions
        "medium_quality": int,              # Medium-quality extractions
        "low_quality": int                  # Low-quality extractions
    }
}

# Output Directory Structure (Windows-compatible paths)
data\\processed\\phase06_5\\
├── advanced_features\\             # ML-ready feature vectors
│   ├── {song_name}_features.json  # List of advanced_features Dicts
│   └── ...
├── feature_matrices\\              # Numpy arrays for ML training
│   ├── {song_name}_features.npy   # Feature matrix (N_candidates x 128)
│   ├── {song_name}_spectrograms.npy # Mel spectrograms (N_candidates x 80 x 32)
│   └── ...
├── extraction_stats\\              # Feature extraction statistics
│   ├── {song_name}_stats.json     # Per-song extraction statistics
│   └── ...
├── quality_reports\\               # Feature quality assessments
│   ├── {song_name}_quality.json   # Per-song quality reports
│   └── ...
└── extraction_summary.json        # Overall extraction statistics
```

### **Data Flow Validation**
```python
# Input validation requirements
input_validation: Dict = {
    "required_files": [
        "{song_name}_candidates.json",      # From Phase 6
        "{song_name}_features.json",        # From Phase 6
        "{song_name}_stats.json"            # From Phase 6
    ],
    "candidate_requirements": {
        "min_candidates_per_song": 10,      # Minimum candidates needed
        "max_candidates_per_song": 10000,   # Maximum candidates to process
        "min_audio_snippet_length": 0.1,    # Minimum 100ms audio snippets
        "max_audio_snippet_length": 2.0     # Maximum 2s audio snippets
    },
    "quality_thresholds": {
        "min_candidate_confidence": 0.3,    # Minimum candidate confidence
        "min_onset_strength": 0.1,          # Minimum onset strength
        "max_noise_level": 0.8              # Maximum acceptable noise level
    }
}

# Output validation requirements
output_validation: Dict = {
    "required_outputs": [
        "{song_name}_features.json",        # Advanced features
        "{song_name}_features.npy",         # Feature matrix
        "{song_name}_spectrograms.npy"      # Mel spectrograms
    ],
    "feature_requirements": {
        "feature_vector_size": 128,         # Expected feature vector size
        "spectrogram_shape": [80, 32],      # Expected spectrogram dimensions
        "min_feature_completeness": 0.9,    # Minimum feature completeness
        "min_extraction_confidence": 0.7    # Minimum extraction confidence
    },
    "performance_limits": {
        "max_processing_time_per_candidate": 100,  # Max 100ms per candidate
        "max_memory_usage_mb": 4096,        # Maximum memory usage
        "max_gpu_memory_mb": 2048           # Maximum GPU memory usage
    }
}
```

### **Inter-Phase Compatibility**
- **Phase 7 Requirements**: Feature vectors with shape [128,] and spectrograms [80, 32]
- **Data Format Consistency**: All features normalized to [0,1] range where applicable
- **File Naming Convention**: `{song_name}_{data_type}.{extension}`
- **Cross-Reference Integrity**: All candidate_ids must match between Phase 6 and Phase 6.5

---

## 3. **Implementation Plan**

### **Python 3.12 Environment Setup**
```python
# Required packages for OGG audio feature extraction (RTX 3070 optimized)
import librosa                      # Advanced audio analysis
import soundfile as sf              # Audio file I/O
import numpy as np                  # Numerical computing
import scipy.signal                 # Signal processing
import madmom                       # Music information retrieval
import torch                        # GPU-accelerated computing
import torchaudio                   # Audio processing with GPU support
from pyAudioAnalysis import audioFeatureExtraction
import matplotlib.pyplot as plt     # Visualization
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
import time
import psutil
import gc
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# RTX 3070 GPU optimization
torch.backends.cudnn.benchmark = True
torch.backends.cudnn.deterministic = False
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
```

### **Core Feature Extraction Engine**
```python
class AdvancedFeatureExtractor:
    """
    Advanced feature extraction engine optimized for RTX 3070.
    
    Features:
    - GPU-accelerated spectral analysis
    - Multi-threaded processing for CPU-bound operations
    - Memory-efficient batch processing
    - Comprehensive feature set for ML training
    """
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or self._get_default_config()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Initialize GPU-accelerated transforms
        self._setup_gpu_transforms()
        
        # Memory management for RTX 3070
        self.max_batch_size = self.config['memory']['max_batch_size']
        self.memory_threshold_gb = self.config['memory']['threshold_gb']
        
        print(f"🚀 AdvancedFeatureExtractor initialized on {self.device}")
        print(f"📊 Max batch size: {self.max_batch_size}")
        print(f"💾 Memory threshold: {self.memory_threshold_gb}GB")
    
    def _get_default_config(self) -> Dict:
        """Get default RTX 3070 optimized configuration."""
        return {
            "spectral": {
                "n_mels": 80,               # Mel filterbank size
                "n_fft": 2048,              # FFT window size
                "hop_length": 512,          # Hop length
                "n_mfcc": 13,               # MFCC coefficients
                "fmin": 20,                 # Minimum frequency
                "fmax": 8000                # Maximum frequency
            },
            "rhythmic": {
                "tempo_range": [60, 200],   # BPM range
                "beat_track_units": "time", # Beat tracking units
                "onset_backtrack": True     # Onset backtracking
            },
            "harmonic": {
                "pitch_method": "yin",      # Pitch detection method
                "pitch_threshold": 0.1,     # Pitch confidence threshold
                "harmonic_margin": 1.0      # Harmonic detection margin
            },
            "memory": {
                "max_batch_size": 32,       # RTX 3070 optimized batch size
                "threshold_gb": 6.0,        # Memory usage threshold
                "cleanup_interval": 100     # Cleanup every N candidates
            },
            "quality": {
                "min_snr_db": 10.0,         # Minimum SNR for quality
                "min_duration_ms": 50,      # Minimum window duration
                "max_noise_ratio": 0.3      # Maximum noise ratio
            }
        }
    
    def _setup_gpu_transforms(self):
        """Setup GPU-accelerated transforms."""
        
        # Mel spectrogram transform
        self.mel_transform = torchaudio.transforms.MelSpectrogram(
            sample_rate=22050,
            n_fft=self.config['spectral']['n_fft'],
            hop_length=self.config['spectral']['hop_length'],
            n_mels=self.config['spectral']['n_mels'],
            f_min=self.config['spectral']['fmin'],
            f_max=self.config['spectral']['fmax']
        ).to(self.device)
        
        # MFCC transform
        self.mfcc_transform = torchaudio.transforms.MFCC(
            sample_rate=22050,
            n_mfcc=self.config['spectral']['n_mfcc'],
            melkwargs={
                "n_fft": self.config['spectral']['n_fft'],
                "hop_length": self.config['spectral']['hop_length'],
                "n_mels": self.config['spectral']['n_mels']
            }
        ).to(self.device)
    
    def extract_features_batch(self, candidates: List[Dict]) -> List[Dict]:
        """
        Extract advanced features from a batch of candidates.
        
        Args:
            candidates: List of candidate dictionaries from Phase 6
            
        Returns:
            List of advanced feature dictionaries for Phase 7
        """
        
        print(f"🔧 Processing batch of {len(candidates)} candidates")
        start_time = time.time()
        
        # Monitor GPU memory
        if torch.cuda.is_available():
            initial_memory = torch.cuda.memory_allocated() / 1024**3
            print(f"💾 Initial GPU memory: {initial_memory:.2f}GB")
        
        advanced_features = []
        
        # Process in smaller batches to manage memory
        batch_size = min(self.max_batch_size, len(candidates))
        
        for i in range(0, len(candidates), batch_size):
            batch = candidates[i:i + batch_size]
            
            # Extract features for current batch
            batch_features = self._process_candidate_batch(batch)
            advanced_features.extend(batch_features)
            
            # Memory cleanup
            if i % self.config['memory']['cleanup_interval'] == 0:
                self._cleanup_memory()
            
            # Progress update
            progress = (i + len(batch)) / len(candidates) * 100
            print(f"📈 Progress: {progress:.1f}% ({i + len(batch)}/{len(candidates)})")
        
        processing_time = time.time() - start_time
        print(f"✅ Batch processing completed in {processing_time:.2f}s")
        print(f"⚡ Average time per candidate: {processing_time/len(candidates)*1000:.1f}ms")
        
        return advanced_features

    def _process_candidate_batch(self, batch: List[Dict]) -> List[Dict]:
        """Process a batch of candidates with GPU acceleration."""

        batch_features = []

        # Prepare audio tensors for GPU processing
        audio_tensors = []
        for candidate in batch:
            audio_snippet = candidate['audio_snippet']
            audio_tensor = torch.FloatTensor(audio_snippet).unsqueeze(0).to(self.device)
            audio_tensors.append(audio_tensor)

        # Stack tensors for batch processing
        if audio_tensors:
            batch_audio = torch.cat(audio_tensors, dim=0)

            # Extract spectral features on GPU
            spectral_features = self._extract_spectral_features_gpu(batch_audio)

            # Extract other features (CPU-bound)
            for i, candidate in enumerate(batch):
                try:
                    # Get spectral features for this candidate
                    candidate_spectral = {
                        key: value[i] if isinstance(value, (list, np.ndarray)) else value
                        for key, value in spectral_features.items()
                    }

                    # Extract remaining features
                    rhythmic_features = self._extract_rhythmic_features(candidate['audio_snippet'])
                    harmonic_features = self._extract_harmonic_features(candidate['audio_snippet'])
                    energy_features = self._extract_energy_features(candidate['audio_snippet'])
                    contextual_features = self._extract_contextual_features(candidate, batch)

                    # Combine all features
                    feature_vector = self._combine_features(
                        candidate_spectral, rhythmic_features,
                        harmonic_features, energy_features, contextual_features
                    )

                    # Quality assessment
                    quality_metrics = self._assess_feature_quality(
                        candidate['audio_snippet'], feature_vector
                    )

                    # Create advanced feature dictionary
                    advanced_feature = {
                        "candidate_id": candidate['window_id'],
                        "window_id": candidate['window_id'],
                        "feature_vector": feature_vector,
                        "spectral_features": candidate_spectral,
                        "rhythmic_features": rhythmic_features,
                        "harmonic_features": harmonic_features,
                        "energy_features": energy_features,
                        "contextual_features": contextual_features,
                        "quality_metrics": quality_metrics
                    }

                    batch_features.append(advanced_feature)

                except Exception as e:
                    print(f"⚠️  Error processing candidate {candidate['window_id']}: {str(e)}")
                    # Create minimal feature set for failed extractions
                    batch_features.append(self._create_fallback_features(candidate))

        return batch_features

    def _extract_spectral_features_gpu(self, batch_audio: torch.Tensor) -> Dict:
        """Extract spectral features using GPU acceleration."""

        with torch.no_grad():
            # Mel spectrogram
            mel_spec = self.mel_transform(batch_audio)
            mel_spec_db = torchaudio.transforms.AmplitudeToDB()(mel_spec)

            # MFCC
            mfcc = self.mfcc_transform(batch_audio)

            # Convert to numpy for compatibility
            mel_spec_np = mel_spec_db.cpu().numpy()
            mfcc_np = mfcc.cpu().numpy()

            # Additional spectral features using librosa (CPU)
            batch_size = batch_audio.shape[0]
            spectral_centroids = []
            spectral_rolloffs = []
            spectral_bandwidths = []
            zero_crossing_rates = []
            chroma_features = []
            tonnetz_features = []

            for i in range(batch_size):
                audio_np = batch_audio[i].cpu().numpy()

                # Spectral features
                centroid = librosa.feature.spectral_centroid(y=audio_np, sr=22050)[0]
                rolloff = librosa.feature.spectral_rolloff(y=audio_np, sr=22050)[0]
                bandwidth = librosa.feature.spectral_bandwidth(y=audio_np, sr=22050)[0]
                zcr = librosa.feature.zero_crossing_rate(audio_np)[0]

                # Harmonic features
                chroma = librosa.feature.chroma_stft(y=audio_np, sr=22050)
                tonnetz = librosa.feature.tonnetz(y=audio_np, sr=22050)

                spectral_centroids.append(np.mean(centroid))
                spectral_rolloffs.append(np.mean(rolloff))
                spectral_bandwidths.append(np.mean(bandwidth))
                zero_crossing_rates.append(np.mean(zcr))
                chroma_features.append(np.mean(chroma, axis=1))
                tonnetz_features.append(np.mean(tonnetz, axis=1))

            return {
                "mel_spectrogram": mel_spec_np,
                "mfcc": mfcc_np,
                "spectral_centroid": spectral_centroids,
                "spectral_rolloff": spectral_rolloffs,
                "spectral_bandwidth": spectral_bandwidths,
                "zero_crossing_rate": zero_crossing_rates,
                "chroma": chroma_features,
                "tonnetz": tonnetz_features
            }

    def _extract_rhythmic_features(self, audio_snippet: np.ndarray) -> Dict:
        """Extract rhythmic features from audio snippet."""

        try:
            # Tempo estimation
            tempo, beats = librosa.beat.beat_track(
                y=audio_snippet,
                sr=22050,
                start_bpm=self.config['rhythmic']['tempo_range'][0],
                units=self.config['rhythmic']['beat_track_units']
            )

            # Onset detection
            onset_frames = librosa.onset.onset_detect(
                y=audio_snippet,
                sr=22050,
                backtrack=self.config['rhythmic']['onset_backtrack']
            )
            onset_times = librosa.frames_to_time(onset_frames, sr=22050)

            # Beat strength
            beat_strength = np.mean(librosa.beat.beat_track(
                y=audio_snippet, sr=22050, return_tempo=False
            )) if len(beats) > 0 else 0.0

            # Rhythm pattern (simplified)
            rhythm_pattern = np.histogram(
                onset_times % (60.0 / tempo) if tempo > 0 else onset_times,
                bins=16, range=(0, 60.0 / tempo if tempo > 0 else 1.0)
            )[0].astype(float)
            rhythm_pattern = rhythm_pattern / (np.sum(rhythm_pattern) + 1e-8)

            return {
                "tempo_estimate": float(tempo),
                "beat_strength": float(beat_strength),
                "rhythm_pattern": rhythm_pattern,
                "onset_density": len(onset_times) / (len(audio_snippet) / 22050),
                "rhythmic_regularity": self._calculate_rhythmic_regularity(onset_times)
            }

        except Exception as e:
            print(f"⚠️  Rhythmic feature extraction failed: {str(e)}")
            return self._get_default_rhythmic_features()

    def _extract_harmonic_features(self, audio_snippet: np.ndarray) -> Dict:
        """Extract harmonic features from audio snippet."""

        try:
            # Pitch detection using YIN algorithm
            f0 = librosa.yin(
                audio_snippet,
                fmin=librosa.note_to_hz('C2'),
                fmax=librosa.note_to_hz('C7'),
                threshold=self.config['harmonic']['pitch_threshold']
            )

            # Filter out unvoiced frames
            voiced_frames = f0 > 0

            if np.any(voiced_frames):
                pitch_estimate = np.median(f0[voiced_frames])
                pitch_confidence = np.sum(voiced_frames) / len(f0)
            else:
                pitch_estimate = 0.0
                pitch_confidence = 0.0

            # Harmonic-to-noise ratio
            harmonic, percussive = librosa.effects.hpss(audio_snippet)
            harmonic_energy = np.sum(harmonic ** 2)
            percussive_energy = np.sum(percussive ** 2)
            harmonic_ratio = harmonic_energy / (harmonic_energy + percussive_energy + 1e-8)

            # Inharmonicity (simplified measure)
            stft = librosa.stft(audio_snippet)
            magnitude = np.abs(stft)
            inharmonicity = np.std(magnitude) / (np.mean(magnitude) + 1e-8)

            # Pitch stability
            pitch_stability = 1.0 - (np.std(f0[voiced_frames]) / (np.mean(f0[voiced_frames]) + 1e-8)) if np.any(voiced_frames) else 0.0

            return {
                "pitch_estimate": float(pitch_estimate),
                "pitch_confidence": float(pitch_confidence),
                "harmonic_ratio": float(harmonic_ratio),
                "inharmonicity": float(inharmonicity),
                "pitch_stability": float(pitch_stability)
            }

        except Exception as e:
            print(f"⚠️  Harmonic feature extraction failed: {str(e)}")
            return self._get_default_harmonic_features()

    def _extract_energy_features(self, audio_snippet: np.ndarray) -> Dict:
        """Extract energy-based features from audio snippet."""

        try:
            # RMS energy
            rms_energy = float(np.sqrt(np.mean(audio_snippet ** 2)))

            # Energy variance
            frame_length = 1024
            hop_length = 512
            rms_frames = librosa.feature.rms(
                y=audio_snippet,
                frame_length=frame_length,
                hop_length=hop_length
            )[0]
            energy_variance = float(np.var(rms_frames))

            # Attack and decay times (simplified)
            envelope = np.abs(audio_snippet)
            smoothed_envelope = scipy.signal.savgol_filter(envelope, 51, 3)

            # Find attack time (time to reach 90% of peak)
            peak_idx = np.argmax(smoothed_envelope)
            peak_value = smoothed_envelope[peak_idx]
            attack_threshold = 0.9 * peak_value

            attack_indices = np.where(smoothed_envelope[:peak_idx] >= attack_threshold)[0]
            attack_time = float(attack_indices[0] / 22050 * 1000) if len(attack_indices) > 0 else 0.0

            # Find decay time (time from peak to 10% of peak)
            decay_threshold = 0.1 * peak_value
            decay_indices = np.where(smoothed_envelope[peak_idx:] <= decay_threshold)[0]
            decay_time = float(decay_indices[0] / 22050 * 1000) if len(decay_indices) > 0 else 0.0

            # Sustain level (average energy in middle 50% of signal)
            mid_start = len(audio_snippet) // 4
            mid_end = 3 * len(audio_snippet) // 4
            sustain_level = float(np.mean(smoothed_envelope[mid_start:mid_end]))

            # Dynamic range
            dynamic_range = float(20 * np.log10((np.max(envelope) + 1e-8) / (np.min(envelope) + 1e-8)))

            return {
                "rms_energy": rms_energy,
                "energy_variance": energy_variance,
                "attack_time": attack_time,
                "decay_time": decay_time,
                "sustain_level": sustain_level,
                "dynamic_range": dynamic_range
            }

        except Exception as e:
            print(f"⚠️  Energy feature extraction failed: {str(e)}")
            return self._get_default_energy_features()

    def _extract_contextual_features(self, candidate: Dict, batch: List[Dict]) -> Dict:
        """Extract contextual features considering surrounding candidates."""

        try:
            current_time = candidate['center_time']

            # Find preceding and following candidates
            preceding_candidates = [c for c in batch if c['center_time'] < current_time]
            following_candidates = [c for c in batch if c['center_time'] > current_time]

            # Get closest preceding and following
            preceding_context = np.zeros(32)  # Default context vector
            following_context = np.zeros(32)

            if preceding_candidates:
                closest_preceding = max(preceding_candidates, key=lambda x: x['center_time'])
                preceding_context = self._extract_basic_features(closest_preceding['audio_snippet'])

            if following_candidates:
                closest_following = min(following_candidates, key=lambda x: x['center_time'])
                following_context = self._extract_basic_features(closest_following['audio_snippet'])

            # Beat context (simplified)
            beat_context = np.array([
                candidate['beat_position'],
                candidate['onset_strength'],
                candidate['candidate_confidence']
            ] + [0.0] * 29)  # Pad to 32 dimensions

            return {
                "preceding_context": preceding_context,
                "following_context": following_context,
                "beat_context": beat_context,
                "measure_position": candidate['beat_position'],  # Simplified
                "phrase_position": (current_time % 8.0) / 8.0   # Simplified 8-second phrases
            }

        except Exception as e:
            print(f"⚠️  Contextual feature extraction failed: {str(e)}")
            return self._get_default_contextual_features()

    def _combine_features(self, spectral: Dict, rhythmic: Dict, harmonic: Dict,
                         energy: Dict, contextual: Dict) -> np.ndarray:
        """Combine all features into a single feature vector."""

        feature_list = []

        # Spectral features (13 MFCC + 4 spectral stats + 12 chroma + 6 tonnetz = 35)
        feature_list.extend(spectral['mfcc'].flatten()[:13])
        feature_list.extend([
            spectral['spectral_centroid'],
            spectral['spectral_rolloff'],
            spectral['spectral_bandwidth'],
            spectral['zero_crossing_rate']
        ])
        feature_list.extend(spectral['chroma'][:12])
        feature_list.extend(spectral['tonnetz'][:6])

        # Rhythmic features (21 features)
        feature_list.append(rhythmic['tempo_estimate'])
        feature_list.append(rhythmic['beat_strength'])
        feature_list.extend(rhythmic['rhythm_pattern'][:16])
        feature_list.append(rhythmic['onset_density'])
        feature_list.append(rhythmic['rhythmic_regularity'])

        # Harmonic features (5 features)
        feature_list.extend([
            harmonic['pitch_estimate'],
            harmonic['pitch_confidence'],
            harmonic['harmonic_ratio'],
            harmonic['inharmonicity'],
            harmonic['pitch_stability']
        ])

        # Energy features (6 features)
        feature_list.extend([
            energy['rms_energy'],
            energy['energy_variance'],
            energy['attack_time'],
            energy['decay_time'],
            energy['sustain_level'],
            energy['dynamic_range']
        ])

        # Contextual features (simplified to fit 128 total)
        feature_list.extend([
            contextual['measure_position'],
            contextual['phrase_position']
        ])

        # Pad or truncate to exactly 128 features
        feature_vector = np.array(feature_list[:128])
        if len(feature_vector) < 128:
            feature_vector = np.pad(feature_vector, (0, 128 - len(feature_vector)))

        # Normalize features
        feature_vector = (feature_vector - np.mean(feature_vector)) / (np.std(feature_vector) + 1e-8)

        return feature_vector.astype(np.float32)

    def _assess_feature_quality(self, audio_snippet: np.ndarray, feature_vector: np.ndarray) -> Dict:
        """Assess the quality of extracted features."""

        # Feature completeness (percentage of non-zero features)
        non_zero_features = np.count_nonzero(feature_vector)
        feature_completeness = non_zero_features / len(feature_vector)

        # Signal quality assessment
        snr_db = self._calculate_snr(audio_snippet)
        signal_quality = min(1.0, max(0.0, (snr_db - 5.0) / 20.0))  # Normalize 5-25 dB to 0-1

        # Extraction confidence based on signal quality and completeness
        extraction_confidence = (feature_completeness + signal_quality) / 2.0

        # Noise level estimation
        noise_level = 1.0 - signal_quality

        return {
            "feature_completeness": float(feature_completeness),
            "extraction_confidence": float(extraction_confidence),
            "noise_level": float(noise_level),
            "signal_quality": float(signal_quality)
        }

    def _cleanup_memory(self):
        """Clean up GPU and CPU memory."""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()

    # Helper methods for default features and error handling
    def _get_default_rhythmic_features(self) -> Dict:
        return {
            "tempo_estimate": 120.0,
            "beat_strength": 0.5,
            "rhythm_pattern": np.ones(16) / 16,
            "onset_density": 1.0,
            "rhythmic_regularity": 0.5
        }

    def _get_default_harmonic_features(self) -> Dict:
        return {
            "pitch_estimate": 440.0,
            "pitch_confidence": 0.0,
            "harmonic_ratio": 0.5,
            "inharmonicity": 0.5,
            "pitch_stability": 0.5
        }

    def _get_default_energy_features(self) -> Dict:
        return {
            "rms_energy": 0.1,
            "energy_variance": 0.01,
            "attack_time": 10.0,
            "decay_time": 100.0,
            "sustain_level": 0.05,
            "dynamic_range": 20.0
        }

    def _get_default_contextual_features(self) -> Dict:
        return {
            "preceding_context": np.zeros(32),
            "following_context": np.zeros(32),
            "beat_context": np.zeros(32),
            "measure_position": 0.5,
            "phrase_position": 0.5
        }

    def _extract_basic_features(self, audio_snippet: np.ndarray) -> np.ndarray:
        """Extract basic features for context."""
        try:
            mfcc = librosa.feature.mfcc(y=audio_snippet, sr=22050, n_mfcc=13)
            return np.mean(mfcc, axis=1)[:32]  # Take first 32 or pad
        except:
            return np.zeros(32)

    def _calculate_snr(self, audio_snippet: np.ndarray) -> float:
        """Calculate signal-to-noise ratio."""
        try:
            # Simple SNR estimation
            signal_power = np.mean(audio_snippet ** 2)
            noise_power = np.var(audio_snippet - scipy.signal.savgol_filter(audio_snippet, 51, 3))
            snr_db = 10 * np.log10((signal_power + 1e-8) / (noise_power + 1e-8))
            return float(snr_db)
        except:
            return 10.0  # Default SNR

    def _calculate_rhythmic_regularity(self, onset_times: np.ndarray) -> float:
        """Calculate rhythmic regularity measure."""
        if len(onset_times) < 2:
            return 0.0

        intervals = np.diff(onset_times)
        if len(intervals) < 2:
            return 0.0

        regularity = 1.0 - (np.std(intervals) / (np.mean(intervals) + 1e-8))
        return float(max(0.0, min(1.0, regularity)))

    def _create_fallback_features(self, candidate: Dict) -> Dict:
        """Create minimal feature set for failed extractions."""
        return {
            "candidate_id": candidate['window_id'],
            "window_id": candidate['window_id'],
            "feature_vector": np.zeros(128, dtype=np.float32),
            "spectral_features": {
                "mel_spectrogram": np.zeros((80, 32)),
                "mfcc": np.zeros(13),
                "spectral_centroid": 0.0,
                "spectral_rolloff": 0.0,
                "spectral_bandwidth": 0.0,
                "zero_crossing_rate": 0.0,
                "chroma": np.zeros(12),
                "tonnetz": np.zeros(6)
            },
            "rhythmic_features": self._get_default_rhythmic_features(),
            "harmonic_features": self._get_default_harmonic_features(),
            "energy_features": self._get_default_energy_features(),
            "contextual_features": self._get_default_contextual_features(),
            "quality_metrics": {
                "feature_completeness": 0.0,
                "extraction_confidence": 0.0,
                "noise_level": 1.0,
                "signal_quality": 0.0
            }
        }

# Main processing function
def extract_advanced_features(
    input_dir: Path,
    output_dir: Path,
    config: Optional[Dict] = None,
    parallel_workers: int = 4
) -> Dict:
    """
    Main function to extract advanced features from Phase 6 candidates.

    Args:
        input_dir: Input directory with Phase 6 outputs
        output_dir: Output directory for Phase 6.5 results
        config: Feature extraction configuration
        parallel_workers: Number of parallel workers

    Returns:
        Processing statistics and results
    """

    print("🚀 Starting Advanced Feature Extraction (Phase 6.5)")
    print(f"📂 Input directory: {input_dir}")
    print(f"📁 Output directory: {output_dir}")

    # Create output directories
    output_dir.mkdir(parents=True, exist_ok=True)
    (output_dir / "advanced_features").mkdir(exist_ok=True)
    (output_dir / "feature_matrices").mkdir(exist_ok=True)
    (output_dir / "extraction_stats").mkdir(exist_ok=True)
    (output_dir / "quality_reports").mkdir(exist_ok=True)

    # Initialize feature extractor
    extractor = AdvancedFeatureExtractor(config)

    # Find all candidate files
    candidate_files = list((input_dir / "note_candidates").glob("*.json"))
    print(f"📊 Found {len(candidate_files)} candidate files to process")

    overall_stats = {
        "total_files": len(candidate_files),
        "processed_files": 0,
        "total_candidates": 0,
        "successful_extractions": 0,
        "processing_time": 0.0,
        "average_time_per_file": 0.0,
        "quality_distribution": {"high": 0, "medium": 0, "low": 0}
    }

    start_time = time.time()

    # Process each file
    for candidate_file in tqdm(candidate_files, desc="Processing files"):
        try:
            file_start_time = time.time()

            # Load candidates
            with open(candidate_file, 'r') as f:
                candidates = json.load(f)

            if not candidates:
                continue

            # Extract features
            advanced_features = extractor.extract_features_batch(candidates)

            # Save results
            filename = candidate_file.stem

            # Save advanced features
            with open(output_dir / "advanced_features" / f"{filename}.json", 'w') as f:
                json.dump(advanced_features, f, indent=2, default=lambda x: x.tolist() if isinstance(x, np.ndarray) else x)

            # Save feature matrices
            if advanced_features:
                feature_matrix = np.array([f['feature_vector'] for f in advanced_features])
                np.save(output_dir / "feature_matrices" / f"{filename}_features.npy", feature_matrix)

                # Save spectrograms if available
                spectrograms = []
                for f in advanced_features:
                    if 'mel_spectrogram' in f['spectral_features']:
                        spectrograms.append(f['spectral_features']['mel_spectrogram'])

                if spectrograms:
                    spectrogram_array = np.array(spectrograms)
                    np.save(output_dir / "feature_matrices" / f"{filename}_spectrograms.npy", spectrogram_array)

            # Calculate file statistics
            file_stats = {
                "filename": filename,
                "total_candidates": len(candidates),
                "successful_extractions": len(advanced_features),
                "extraction_success_rate": len(advanced_features) / len(candidates) if candidates else 0.0,
                "processing_time": time.time() - file_start_time,
                "average_quality": np.mean([f['quality_metrics']['extraction_confidence'] for f in advanced_features]) if advanced_features else 0.0
            }

            # Save file statistics
            with open(output_dir / "extraction_stats" / f"{filename}.json", 'w') as f:
                json.dump(file_stats, f, indent=2)

            # Update overall statistics
            overall_stats["processed_files"] += 1
            overall_stats["total_candidates"] += len(candidates)
            overall_stats["successful_extractions"] += len(advanced_features)

            # Quality distribution
            for feature in advanced_features:
                quality = feature['quality_metrics']['extraction_confidence']
                if quality >= 0.8:
                    overall_stats["quality_distribution"]["high"] += 1
                elif quality >= 0.5:
                    overall_stats["quality_distribution"]["medium"] += 1
                else:
                    overall_stats["quality_distribution"]["low"] += 1

            print(f"✅ Processed {filename}: {len(advanced_features)}/{len(candidates)} features extracted")

        except Exception as e:
            print(f"❌ Error processing {candidate_file}: {str(e)}")
            continue

    # Calculate final statistics
    total_time = time.time() - start_time
    overall_stats["processing_time"] = total_time
    overall_stats["average_time_per_file"] = total_time / len(candidate_files) if candidate_files else 0.0
    overall_stats["overall_success_rate"] = (overall_stats["successful_extractions"] /
                                           overall_stats["total_candidates"]) if overall_stats["total_candidates"] > 0 else 0.0

    # Save overall statistics
    with open(output_dir / "extraction_summary.json", 'w') as f:
        json.dump(overall_stats, f, indent=2)

    print(f"🎉 Advanced Feature Extraction Complete!")
    print(f"📊 Processed {overall_stats['processed_files']}/{overall_stats['total_files']} files")
    print(f"🎯 Success rate: {overall_stats['overall_success_rate']:.2%}")
    print(f"⏱️  Total time: {total_time:.2f}s")
    print(f"💾 Results saved to: {output_dir}")

    return overall_stats

# Example usage
if __name__ == "__main__":
    input_directory = Path("data\\processed\\phase6")
    output_directory = Path("data\\processed\\phase6_5")

    results = extract_advanced_features(
        input_dir=input_directory,
        output_dir=output_directory,
        parallel_workers=4
    )

    print(f"Final results: {results}")
```

---

## 4. **Best Practices**

### **RTX 3070 Memory Management**
- **Batch Processing**: Process candidates in batches of 32 to stay within 8GB VRAM limit
- **Mixed Precision**: Use FP16 for GPU operations to reduce memory usage by 50%
- **Memory Monitoring**: Continuously monitor GPU memory and trigger cleanup when needed
- **Gradient Checkpointing**: Not applicable for inference, but useful for any training operations

### **Feature Quality Assurance**
- **SNR Filtering**: Only process candidates with SNR > 10dB for reliable features
- **Completeness Checking**: Ensure feature vectors have >80% non-zero values
- **Fallback Mechanisms**: Provide default features for failed extractions
- **Quality Scoring**: Assign confidence scores to all extracted features

### **Windows Compatibility**
- **Path Handling**: Use `Path` objects for cross-platform compatibility
- **File Encoding**: Specify UTF-8 encoding for JSON files
- **Memory Management**: Use explicit garbage collection for large datasets
- **Error Handling**: Robust exception handling for Windows-specific issues

---

## 5. **Challenges & Pitfalls**

### **Memory Constraints**
- **Issue**: RTX 3070 8GB VRAM limitation with large spectrograms
- **Example**: Processing 128 candidates with 80x32 mel spectrograms = ~10GB
- **Mitigation**: Batch processing with dynamic batch size adjustment
- **Solution**: Monitor memory usage and reduce batch size when approaching limits

### **Feature Dimensionality**
- **Issue**: High-dimensional features may cause overfitting in Phase 7
- **Example**: 128-dimensional feature vectors for thousands of candidates
- **Mitigation**: Feature selection and dimensionality reduction techniques
- **Solution**: PCA or feature importance analysis to reduce dimensions

### **Processing Speed**
- **Issue**: Complex feature extraction may be too slow for large datasets
- **Example**: 10,000 candidates taking >1 hour to process
- **Mitigation**: GPU acceleration for spectral features, parallel processing for others
- **Solution**: Optimize bottleneck operations and use efficient algorithms

### **OGG Format Compatibility**
- **Issue**: Some OGG files may have encoding issues or corruption
- **Example**: Variable bitrate OGG files causing librosa loading errors
- **Mitigation**: Robust error handling and format validation
- **Solution**: Convert problematic files or skip with graceful degradation

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 6 Complete**: Note candidate windows with basic features
- **Python 3.12 Environment**: All required packages installed and configured
- **RTX 3070 Setup**: CUDA drivers and PyTorch GPU support verified
- **Windows Compatibility**: File system and path handling tested

### **What This Phase Unlocks**
- **Phase 7 Ready**: ML-ready feature vectors for note classification
- **Enhanced Accuracy**: Comprehensive features improve classification performance
- **Scalable Processing**: GPU-accelerated pipeline handles large datasets
- **Quality Control**: Feature quality metrics enable data filtering

### **Output Dependencies**
Phase 6.5 provides Phase 7 with:
- `advanced_features\\*.json` - Complete feature dictionaries
- `feature_matrices\\*_features.npy` - NumPy arrays for ML training
- `feature_matrices\\*_spectrograms.npy` - Mel spectrograms for deep learning
- `extraction_stats\\*.json` - Processing statistics and quality metrics

---

## 7. **Validation Strategy**

### **Feature Quality Validation**
```python
def validate_feature_quality(advanced_features: List[Dict]) -> Dict:
    """Validate extracted feature quality."""

    validation_results = {
        "total_features": len(advanced_features),
        "quality_passed": 0,
        "quality_failed": 0,
        "average_completeness": 0.0,
        "average_confidence": 0.0,
        "issues_found": []
    }

    for feature in advanced_features:
        quality = feature['quality_metrics']

        # Check feature completeness
        if quality['feature_completeness'] < 0.8:
            validation_results["issues_found"].append(
                f"Low feature completeness: {quality['feature_completeness']:.2f}"
            )
            validation_results["quality_failed"] += 1
        else:
            validation_results["quality_passed"] += 1

        # Check extraction confidence
        if quality['extraction_confidence'] < 0.5:
            validation_results["issues_found"].append(
                f"Low extraction confidence: {quality['extraction_confidence']:.2f}"
            )

        validation_results["average_completeness"] += quality['feature_completeness']
        validation_results["average_confidence"] += quality['extraction_confidence']

    # Calculate averages
    if advanced_features:
        validation_results["average_completeness"] /= len(advanced_features)
        validation_results["average_confidence"] /= len(advanced_features)

    # Overall validation
    success_rate = validation_results["quality_passed"] / len(advanced_features) if advanced_features else 0.0
    validation_results["validation_passed"] = success_rate >= 0.90  # 90% quality pass rate
    validation_results["success_rate"] = success_rate

    return validation_results

# Quality gates for Phase 6.5
QUALITY_GATES = {
    "min_success_rate": 0.95,           # >95% successful feature extraction
    "min_quality_pass_rate": 0.90,     # >90% features meet quality standards
    "min_feature_completeness": 0.80,  # >80% non-zero features
    "min_extraction_confidence": 0.70, # >70% average confidence
    "max_processing_time_ms": 200      # <200ms per candidate
}
```

### **Example Success Case**
```python
# Expected advanced features format (for Phase 7 compatibility)
advanced_features_example = [
    {
        "candidate_id": 0,
        "window_id": 0,
        "feature_vector": np.array([0.12, -0.34, 0.56, ...]),  # 128 dimensions
        "spectral_features": {
            "mel_spectrogram": np.array([[0.1, 0.2, ...], ...]),  # 80x32
            "mfcc": np.array([1.2, -0.8, 0.3, ...]),              # 13 coefficients
            "spectral_centroid": 2150.5,
            "spectral_rolloff": 4200.8,
            "spectral_bandwidth": 1800.2,
            "zero_crossing_rate": 0.15,
            "chroma": np.array([0.8, 0.2, 0.1, ...]),             # 12 values
            "tonnetz": np.array([0.3, -0.1, 0.4, ...])            # 6 values
        },
        "rhythmic_features": {
            "tempo_estimate": 128.0,
            "beat_strength": 0.85,
            "rhythm_pattern": np.array([0.2, 0.1, 0.3, ...]),    # 16 values
            "onset_density": 2.5,
            "rhythmic_regularity": 0.78
        },
        "harmonic_features": {
            "pitch_estimate": 440.0,
            "pitch_confidence": 0.92,
            "harmonic_ratio": 0.75,
            "inharmonicity": 0.12,
            "pitch_stability": 0.88
        },
        "energy_features": {
            "rms_energy": 0.15,
            "energy_variance": 0.02,
            "attack_time": 12.5,
            "decay_time": 85.3,
            "sustain_level": 0.08,
            "dynamic_range": 24.5
        },
        "contextual_features": {
            "preceding_context": np.array([0.1, 0.2, ...]),       # 32 values
            "following_context": np.array([0.3, 0.1, ...]),       # 32 values
            "beat_context": np.array([0.5, 0.8, 0.9, ...]),       # 32 values
            "measure_position": 0.25,
            "phrase_position": 0.60
        },
        "quality_metrics": {
            "feature_completeness": 0.95,
            "extraction_confidence": 0.88,
            "noise_level": 0.12,
            "signal_quality": 0.88
        }
    }
]

# Expected file outputs:
# data\\processed\\phase6_5\\advanced_features\\Lemon.json - Complete feature dictionaries
# data\\processed\\phase6_5\\feature_matrices\\Lemon_features.npy - Feature matrix (N x 128)
# data\\processed\\phase6_5\\feature_matrices\\Lemon_spectrograms.npy - Spectrograms (N x 80 x 32)
# data\\processed\\phase6_5\\extraction_stats\\Lemon.json - Processing statistics
```

---

**Phase 6.5 Complete**. This phase transforms basic note candidates into comprehensive ML-ready features optimized for RTX 3070 processing, providing Phase 7 with high-quality inputs for accurate note classification.
```
