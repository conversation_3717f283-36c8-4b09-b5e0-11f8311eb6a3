# 🧩 Phase 10: Measure Segmentation & Bar Lines

**Status**: 📋 Planned  
**Estimated Duration**: 3 days  
**Dependencies**: [Phase 5: Tempo Alignment](phase_05_tempo_alignment.md), [Phase 8: Sequence Patterns](phase_08_sequence_patterns.md)  
**Next Phase**: [Phase 11: Go-Go Time & Special Sections](phase_11_special_sections.md)

---

## 1. **Phase Purpose**

This phase segments generated note sequences into musical measures and determines bar line positions for TJA formatting. This step is isolated because:

- **Measure segmentation** requires understanding musical time signatures and structure
- **Bar line placement** follows specific TJA formatting rules
- **Timing validation** ensures measures align with beat positions and BPM
- **Musical structure** provides the framework for organizing notes into playable sections

**Why Isolated**: Measure segmentation requires music theory knowledge different from pattern generation. The measure structure is essential for TJA formatting and affects how players read and interpret the chart.

---

## 2. **Data Flow Specification**

### **Input Data Sources & Dependencies**
- **Primary Source**: Phase 9 outputs (difficulty patterns and note sequences)
- **Secondary Source**: Phase 5 outputs (tempo alignment and beat positions)
- **Metadata Source**: TJA files (time signature information)

### **Input Data Format**
```python
# From Phase 9: Generated note sequences with difficulty patterns (exact format match)
difficulty_patterns: List[Dict] = [  # Generated patterns with difficulty info
    {
        "pattern_id": int,              # Unique pattern identifier
        "difficulty_level": int,        # Target difficulty (8-10)
        "note_sequence": List[Dict],    # Notes in this pattern
        "complexity_score": float,      # Pattern complexity (0-1)
        "duration": float,              # Pattern duration in seconds
        "quality_score": float          # Pattern quality assessment
    }
]

generated_sequences: List[Dict] = [  # Complete note sequences
    {
        "sequence_id": str,             # Unique sequence identifier
        "notes": List[Dict],            # Sequential note data with timing
        "difficulty_level": int,        # Difficulty level for this sequence
        "total_duration": float,        # Sequence duration in seconds
        "note_count": int,              # Total notes in sequence
        "sequence_quality": float       # Overall sequence quality score
    }
]

# From Phase 5: Tempo alignment data (exact format match)
aligned_beats: List[Dict] = [       # Tempo-aligned beat positions
    {
        "beat_id": int,                 # Sequential beat identifier
        "time": float,                  # Beat time in seconds
        "confidence": float,            # Beat detection confidence
        "strength": float,              # Beat strength/salience
        "measure_position": float       # Position within measure (0-1)
    }
]

bpm_validation: Dict = {            # BPM and timing information
    "validated_bpm": float,             # Validated BPM value
    "bpm_confidence": float,            # BPM validation confidence
    "tempo_stability": float,           # Tempo consistency measure
    "timing_accuracy": float            # Overall timing accuracy
}

# From TJA metadata
time_signature_info: Dict = {
    "default_signature": str,       # "4/4", "3/4", etc.
    "signature_changes": List[Dict], # Time signature changes
    "measure_length": float         # Default measure duration
}

# Input Directory Structure (Windows-compatible paths)
data\\processed\\phase09\\
├── difficulty_patterns\\           # Generated patterns
│   ├── {song_name}_patterns.json  # List of difficulty_patterns Dicts
│   └── ...
├── note_sequences\\                # Complete note sequences
│   ├── {song_name}_sequences.json # List of generated_sequences Dicts
│   └── ...
data\\processed\\phase05\\
├── aligned_beats\\                 # Beat timing data
│   ├── {song_name}_beats.json     # List of aligned_beats Dicts
│   └── ...
├── tempo_alignment\\               # BPM information
│   ├── {song_name}_tempo.json     # bpm_validation Dict
│   └── ...
```

### **Output Data Format (Phase 11 Compatible)**
```python
# Measure segmentation results (exact format for Phase 11 consumption)
measure_segments: List[Dict] = [
    {
        "measure_id": int,              # Sequential measure number
        "start_time": float,            # Measure start time (seconds)
        "end_time": float,              # Measure end time (seconds)
        "duration": float,              # Measure duration
        "time_signature": str,          # "4/4", "3/4", etc.
        "beat_count": int,              # Number of beats in measure
        "subdivision_count": int,       # Number of subdivisions (typically 16)
        "notes_in_measure": List[Dict], # Notes within this measure
        "bar_line_position": float,     # Bar line time position
        "measure_complexity": float,    # Complexity score for this measure (0-1)
        "is_complete": bool,            # Whether measure is fully filled
        "note_density": float,          # Notes per beat in this measure
        "difficulty_contribution": float # Contribution to overall difficulty
    }
]

# Bar line information (exact format for Phase 11 consumption)
bar_lines: List[Dict] = [
    {
        "bar_id": int,                  # Sequential bar line number
        "time_position": float,         # Time position (seconds)
        "measure_boundary": bool,       # True if major measure boundary
        "bar_type": str,               # "measure", "phrase", "section"
        "visual_emphasis": int,         # Visual emphasis level (1-3)
        "tja_format": str              # TJA bar line representation
    }
]

# Measure validation results
measure_validation: Dict = {
    "total_measures": int,              # Total number of measures
    "complete_measures": int,           # Fully populated measures
    "incomplete_measures": int,         # Partially filled measures
    "average_measure_duration": float,  # Average measure length (seconds)
    "timing_accuracy": float,           # How well measures align with beats (0-1)
    "measure_consistency": float,       # Consistency of measure lengths (0-1)
    "validation_issues": List[str],     # Any detected problems
    "quality_score": float             # Overall segmentation quality (0-1)
}

# Output Directory Structure (Windows-compatible paths)
data\\processed\\phase10\\
├── measure_segments\\              # Measure segmentation results
│   ├── {song_name}_measures.json  # List of measure_segments Dicts
│   └── ...
├── bar_lines\\                     # Bar line positions
│   ├── {song_name}_bars.json      # List of bar_lines Dicts
│   └── ...
├── measure_validation\\            # Validation results
│   ├── {song_name}_validation.json # measure_validation Dict
│   └── ...
├── timing_analysis\\               # Detailed timing analysis
│   ├── {song_name}_timing.json    # Timing analysis data
│   └── ...
└── segmentation_report.json        # Overall segmentation statistics
```

### **Data Flow Validation**
```python
# Input validation requirements
input_validation: Dict = {
    "required_files": [
        "{song_name}_patterns.json",        # From Phase 9
        "{song_name}_sequences.json",       # From Phase 9
        "{song_name}_beats.json",           # From Phase 5
        "{song_name}_tempo.json"            # From Phase 5
    ],
    "sequence_requirements": {
        "min_notes_per_sequence": 100,      # Minimum notes for segmentation
        "max_notes_per_sequence": 5000,     # Maximum notes to process
        "min_sequence_duration": 30.0,      # Minimum 30 seconds
        "max_sequence_duration": 600.0      # Maximum 10 minutes
    },
    "timing_requirements": {
        "min_beat_confidence": 0.7,         # Minimum beat detection confidence
        "min_tempo_stability": 0.8,         # Minimum tempo consistency
        "max_timing_variance": 0.1          # Maximum timing variance
    }
}

# Output validation requirements
output_validation: Dict = {
    "required_outputs": [
        "{song_name}_measures.json",        # Measure segments
        "{song_name}_bars.json",            # Bar lines
        "{song_name}_validation.json"       # Validation results
    ],
    "segmentation_quality_gates": {
        "min_complete_measures_ratio": 0.8, # Minimum complete measures
        "min_timing_accuracy": 0.9,         # Minimum timing accuracy
        "max_measure_duration_variance": 0.2, # Maximum duration variance
        "min_quality_score": 0.8            # Minimum overall quality
    },
    "performance_limits": {
        "max_processing_time_seconds": 60,  # Maximum processing time
        "max_memory_usage_mb": 1024         # Maximum memory usage
    }
}
```

### **Inter-Phase Compatibility**
- **Phase 11 Requirements**: Measure segments with complexity scores and bar line positions
- **Data Format Consistency**: All time positions in seconds, measure IDs sequential
- **File Naming Convention**: `{song_name}_{data_type}.{extension}`
- **Cross-Reference Integrity**: All note references must be traceable to Phase 9

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import numpy as np
import pandas as pd
from pathlib import Path
import json
import logging
from typing import List, Dict, Tuple, Optional
from tqdm import tqdm
import matplotlib.pyplot as plt
from collections import defaultdict
import math
```

### **Core Measure Segmentation Function**
```python
def segment_into_measures(
    note_sequence: List[Dict],
    aligned_beats: List[Dict],
    bpm: float,
    time_signature: str = "4/4",
    offset: float = 0.0
) -> Tuple[List[Dict], List[Dict]]:
    """
    Segment note sequence into musical measures.
    
    Args:
        note_sequence: List of notes with timing information
        aligned_beats: List of aligned beat positions
        bpm: Beats per minute
        time_signature: Time signature (e.g., "4/4")
        offset: Timing offset in seconds
        
    Returns:
        (measure_segments, bar_lines)
    """
    
    if not note_sequence or not aligned_beats:
        return [], []
    
    # Parse time signature
    beats_per_measure, beat_unit = parse_time_signature(time_signature)
    
    # Calculate measure duration
    beat_duration = 60.0 / bpm  # Duration of one beat in seconds
    measure_duration = beats_per_measure * beat_duration
    
    # Determine measure boundaries based on beats
    measure_boundaries = calculate_measure_boundaries(
        aligned_beats, beats_per_measure, measure_duration, offset
    )
    
    # Segment notes into measures
    measure_segments = []
    bar_lines = []
    
    for i, (start_time, end_time) in enumerate(measure_boundaries):
        # Find notes in this measure
        notes_in_measure = find_notes_in_time_range(
            note_sequence, start_time, end_time
        )
        
        # Calculate measure properties
        measure_complexity = calculate_measure_complexity(notes_in_measure)
        subdivision_count = calculate_subdivision_count(time_signature)
        
        # Create measure segment
        measure_segment = {
            "measure_id": i,
            "start_time": float(start_time),
            "end_time": float(end_time),
            "duration": float(end_time - start_time),
            "time_signature": time_signature,
            "beat_count": beats_per_measure,
            "subdivision_count": subdivision_count,
            "notes_in_measure": notes_in_measure,
            "bar_line_position": float(end_time),
            "measure_complexity": float(measure_complexity),
            "is_complete": len(notes_in_measure) > 0
        }
        
        measure_segments.append(measure_segment)
        
        # Create bar line
        bar_line = {
            "bar_id": i,
            "time_position": float(end_time),
            "measure_boundary": True,
            "bar_type": "measure",
            "visual_emphasis": 2 if (i + 1) % 4 == 0 else 1  # Emphasize every 4th measure
        }
        
        bar_lines.append(bar_line)
    
    return measure_segments, bar_lines

def parse_time_signature(time_signature: str) -> Tuple[int, int]:
    """Parse time signature string into beats per measure and beat unit."""
    
    try:
        numerator, denominator = time_signature.split('/')
        beats_per_measure = int(numerator)
        beat_unit = int(denominator)
        return beats_per_measure, beat_unit
    except (ValueError, AttributeError):
        # Default to 4/4 if parsing fails
        logging.warning(f"Invalid time signature: {time_signature}, defaulting to 4/4")
        return 4, 4

def calculate_measure_boundaries(
    aligned_beats: List[Dict],
    beats_per_measure: int,
    measure_duration: float,
    offset: float
) -> List[Tuple[float, float]]:
    """Calculate measure boundary positions."""
    
    if not aligned_beats:
        return []
    
    # Extract beat times
    beat_times = [beat["beat_time"] for beat in aligned_beats]
    
    # Find the first beat after offset
    start_beat_idx = 0
    for i, beat_time in enumerate(beat_times):
        if beat_time >= offset:
            start_beat_idx = i
            break
    
    # Group beats into measures
    measure_boundaries = []
    current_measure_start = beat_times[start_beat_idx] if start_beat_idx < len(beat_times) else offset
    
    # Calculate measures based on beat positions
    for i in range(start_beat_idx, len(beat_times), beats_per_measure):
        measure_start = beat_times[i]
        
        # Find measure end
        if i + beats_per_measure < len(beat_times):
            measure_end = beat_times[i + beats_per_measure]
        else:
            # Last measure - estimate end time
            measure_end = measure_start + measure_duration
        
        measure_boundaries.append((measure_start, measure_end))
    
    return measure_boundaries

def find_notes_in_time_range(
    note_sequence: List[Dict],
    start_time: float,
    end_time: float
) -> List[Dict]:
    """Find all notes within a specific time range."""
    
    notes_in_range = []
    
    for note in note_sequence:
        note_time = note.get("time", 0.0)
        if start_time <= note_time < end_time:
            notes_in_range.append(note)
    
    return notes_in_range

def calculate_measure_complexity(notes_in_measure: List[Dict]) -> float:
    """Calculate complexity score for a measure."""
    
    if not notes_in_measure:
        return 0.0
    
    # Count different note types
    note_types = [note.get("note_type", "rest") for note in notes_in_measure]
    non_rest_notes = [nt for nt in note_types if nt != "rest"]
    
    # Basic complexity factors
    note_density = len(non_rest_notes) / 16.0  # Assuming 16 subdivisions
    note_variety = len(set(note_types)) / 5.0  # Normalize by max note types
    
    # Pattern complexity (alternating patterns are more complex)
    alternation_score = calculate_alternation_complexity(note_types)
    
    # Combine factors
    complexity = (note_density * 0.4 + note_variety * 0.3 + alternation_score * 0.3)
    
    return min(1.0, complexity)

def calculate_alternation_complexity(note_types: List[str]) -> float:
    """Calculate complexity based on note alternation patterns."""
    
    if len(note_types) < 2:
        return 0.0
    
    # Count alternations between different note types
    alternations = 0
    for i in range(1, len(note_types)):
        if note_types[i] != note_types[i-1] and note_types[i] != "rest":
            alternations += 1
    
    # Normalize by maximum possible alternations
    max_alternations = len(note_types) - 1
    return alternations / max_alternations if max_alternations > 0 else 0.0

def calculate_subdivision_count(time_signature: str) -> int:
    """Calculate number of subdivisions for a time signature."""
    
    beats_per_measure, beat_unit = parse_time_signature(time_signature)
    
    # Standard subdivision mapping
    subdivision_mapping = {
        4: 4,   # Quarter note = 4 sixteenth notes
        8: 2,   # Eighth note = 2 sixteenth notes
        2: 8,   # Half note = 8 sixteenth notes
        1: 16   # Whole note = 16 sixteenth notes
    }
    
    subdivisions_per_beat = subdivision_mapping.get(beat_unit, 4)
    return beats_per_measure * subdivisions_per_beat

def validate_measure_segmentation(
    measure_segments: List[Dict],
    aligned_beats: List[Dict],
    bpm: float
) -> Dict:
    """Validate the quality of measure segmentation."""
    
    validation_results = {
        "total_measures": len(measure_segments),
        "complete_measures": 0,
        "incomplete_measures": 0,
        "average_measure_duration": 0.0,
        "timing_accuracy": 0.0,
        "measure_consistency": 0.0,
        "validation_issues": []
    }
    
    if not measure_segments:
        validation_results["validation_issues"].append("No measures found")
        return validation_results
    
    # Count complete/incomplete measures
    complete_measures = sum(1 for m in measure_segments if m["is_complete"])
    incomplete_measures = len(measure_segments) - complete_measures
    
    validation_results["complete_measures"] = complete_measures
    validation_results["incomplete_measures"] = incomplete_measures
    
    # Calculate average measure duration
    durations = [m["duration"] for m in measure_segments]
    avg_duration = np.mean(durations) if durations else 0.0
    validation_results["average_measure_duration"] = float(avg_duration)
    
    # Expected measure duration based on BPM
    expected_duration = 4 * (60.0 / bmp)  # 4 beats per measure for 4/4 time
    
    # Timing accuracy (how close to expected duration)
    duration_errors = [abs(d - expected_duration) / expected_duration for d in durations]
    timing_accuracy = 1.0 - np.mean(duration_errors) if duration_errors else 0.0
    validation_results["timing_accuracy"] = float(max(0.0, timing_accuracy))
    
    # Measure consistency (how consistent are measure durations)
    duration_std = np.std(durations) if len(durations) > 1 else 0.0
    measure_consistency = 1.0 - (duration_std / avg_duration) if avg_duration > 0 else 0.0
    validation_results["measure_consistency"] = float(max(0.0, measure_consistency))
    
    # Check for validation issues
    if incomplete_measures > len(measure_segments) * 0.1:  # More than 10% incomplete
        validation_results["validation_issues"].append(
            f"High number of incomplete measures: {incomplete_measures}"
        )
    
    if timing_accuracy < 0.8:
        validation_results["validation_issues"].append(
            f"Poor timing accuracy: {timing_accuracy:.2f}"
        )
    
    if measure_consistency < 0.8:
        validation_results["validation_issues"].append(
            f"Inconsistent measure durations: {measure_consistency:.2f}"
        )
    
    return validation_results

def adjust_measure_boundaries(
    measure_segments: List[Dict],
    aligned_beats: List[Dict],
    tolerance: float = 0.1
) -> List[Dict]:
    """Adjust measure boundaries to better align with beats."""
    
    adjusted_segments = []
    
    for measure in measure_segments:
        start_time = measure["start_time"]
        end_time = measure["end_time"]
        
        # Find closest beats to measure boundaries
        closest_start_beat = find_closest_beat(aligned_beats, start_time)
        closest_end_beat = find_closest_beat(aligned_beats, end_time)
        
        # Adjust boundaries if within tolerance
        if closest_start_beat and abs(closest_start_beat["beat_time"] - start_time) < tolerance:
            start_time = closest_start_beat["beat_time"]
        
        if closest_end_beat and abs(closest_end_beat["beat_time"] - end_time) < tolerance:
            end_time = closest_end_beat["beat_time"]
        
        # Update measure with adjusted boundaries
        adjusted_measure = measure.copy()
        adjusted_measure["start_time"] = float(start_time)
        adjusted_measure["end_time"] = float(end_time)
        adjusted_measure["duration"] = float(end_time - start_time)
        adjusted_measure["bar_line_position"] = float(end_time)
        
        adjusted_segments.append(adjusted_measure)
    
    return adjusted_segments

def find_closest_beat(aligned_beats: List[Dict], target_time: float) -> Optional[Dict]:
    """Find the beat closest to a target time."""
    
    if not aligned_beats:
        return None
    
    closest_beat = None
    min_distance = float('inf')
    
    for beat in aligned_beats:
        distance = abs(beat["beat_time"] - target_time)
        if distance < min_distance:
            min_distance = distance
            closest_beat = beat
    
    return closest_beat

def process_measure_segmentation(
    input_dir: Path = Path("data\\processed\\phase9"),
    beat_dir: Path = Path("data\\processed\\phase5"),
    output_dir: Path = Path("data\\processed\\phase10"),
    default_time_signature: str = "4/4"
) -> Dict:
    """Process measure segmentation for entire dataset."""
    
    # Setup output directories
    (output_dir / "measure_segments").mkdir(parents=True, exist_ok=True)
    (output_dir / "bar_lines").mkdir(parents=True, exist_ok=True)
    (output_dir / "measure_validation").mkdir(parents=True, exist_ok=True)
    (output_dir / "timing_analysis").mkdir(parents=True, exist_ok=True)
    
    # Find all pattern files
    pattern_files = list((input_dir / "difficulty_patterns").glob("*.json"))
    
    results = {
        "total_songs": len(pattern_files),
        "processed_songs": 0,
        "total_measures": 0,
        "average_measures_per_song": 0.0,
        "average_timing_accuracy": 0.0,
        "processing_errors": []
    }
    
    all_timing_accuracies = []
    all_measure_counts = []
    
    for pattern_file in tqdm(pattern_files, desc="Segmenting measures"):
        try:
            song_name = pattern_file.stem
            
            # Load pattern data
            with open(pattern_file, 'r') as f:
                pattern_data = json.load(f)
            
            # Load corresponding beat data
            beat_file = beat_dir / "aligned_beats" / f"{song_name}.json"
            if not beat_file.exists():
                logging.warning(f"No beat data found for {song_name}")
                continue
            
            with open(beat_file, 'r') as f:
                beat_data = json.load(f)
            
            # Load tempo alignment data
            tempo_file = beat_dir / "tempo_alignment" / f"{song_name}.json"
            bpm = 120.0  # Default BPM
            offset = 0.0
            
            if tempo_file.exists():
                with open(tempo_file, 'r') as f:
                    tempo_data = json.load(f)
                    bpm = tempo_data.get("aligned_bpm", 120.0)
                    offset = tempo_data.get("alignment_offset", 0.0)
            
            # Extract note sequence from patterns
            note_sequence = extract_note_sequence_from_patterns(pattern_data)
            
            # Segment into measures
            measure_segments, bar_lines = segment_into_measures(
                note_sequence, beat_data, bpm, default_time_signature, offset
            )
            
            # Adjust boundaries for better alignment
            measure_segments = adjust_measure_boundaries(measure_segments, beat_data)
            
            # Validate segmentation
            validation_results = validate_measure_segmentation(
                measure_segments, beat_data, bpm
            )
            
            # Save results
            measure_file = output_dir / "measure_segments" / f"{song_name}.json"
            with open(measure_file, 'w') as f:
                json.dump(measure_segments, f, indent=2)
            
            bar_file = output_dir / "bar_lines" / f"{song_name}.json"
            with open(bar_file, 'w') as f:
                json.dump(bar_lines, f, indent=2)
            
            validation_file = output_dir / "measure_validation" / f"{song_name}.json"
            with open(validation_file, 'w') as f:
                json.dump(validation_results, f, indent=2)
            
            # Update statistics
            results["processed_songs"] += 1
            results["total_measures"] += len(measure_segments)
            all_timing_accuracies.append(validation_results["timing_accuracy"])
            all_measure_counts.append(len(measure_segments))
            
        except Exception as e:
            error_info = {"song": song_name, "error": str(e)}
            results["processing_errors"].append(error_info)
            logging.error(f"Error processing {song_name}: {e}")
    
    # Calculate final statistics
    if all_timing_accuracies:
        results["average_timing_accuracy"] = float(np.mean(all_timing_accuracies))
    if all_measure_counts:
        results["average_measures_per_song"] = float(np.mean(all_measure_counts))
    
    # Save overall results
    with open(output_dir / "segmentation_report.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

def extract_note_sequence_from_patterns(pattern_data: List[Dict]) -> List[Dict]:
    """Extract note sequence from pattern data."""
    
    note_sequence = []
    current_time = 0.0
    
    for pattern in pattern_data:
        if "generated_pattern" in pattern:
            for note_type in pattern["generated_pattern"]:
                note = {
                    "time": current_time,
                    "note_type": note_type
                }
                note_sequence.append(note)
                current_time += 0.125  # Assume 16th note spacing
    
    return note_sequence
```

---

## 4. **Best Practices**

### **Musical Structure Awareness**
- Respect standard time signatures and musical conventions
- Handle time signature changes gracefully
- Consider phrase boundaries and musical sections
- Align measures with natural musical groupings

### **Timing Precision**
```python
# High-precision timing calculations
def calculate_precise_measure_duration(bpm: float, time_signature: str) -> float:
    """Calculate precise measure duration accounting for tempo variations."""
    
    beats_per_measure, beat_unit = parse_time_signature(time_signature)
    
    # Account for beat unit (quarter note = 4, eighth note = 8, etc.)
    beat_duration = (60.0 / bpm) * (4.0 / beat_unit)
    measure_duration = beats_per_measure * beat_duration
    
    return measure_duration
```

### **Boundary Adjustment**
- Use beat positions as reference points for measure boundaries
- Apply tolerance-based snapping to nearby beats
- Maintain consistent measure lengths within songs
- Handle edge cases (incomplete measures, tempo changes)

### **Validation and Quality Control**
- Check measure duration consistency
- Validate alignment with beat positions
- Ensure complete measure coverage
- Flag problematic segmentations for review

---

## 5. **Challenges & Pitfalls**

### **Irregular Time Signatures**
- **Issue**: Non-standard time signatures (5/4, 7/8, etc.) are harder to segment
- **Example**: Progressive rock, world music with complex meters
- **Mitigation**: Implement support for common irregular signatures
- **Solution**: Use adaptive segmentation based on beat patterns

### **Tempo Changes**
- **Issue**: Songs with tempo changes break regular measure segmentation
- **Symptoms**: Measures of varying lengths, poor alignment
- **Mitigation**: Detect tempo changes and adjust segmentation accordingly
- **Solution**: Implement tempo-aware segmentation with variable measure lengths

### **Beat Detection Errors**
- **Issue**: Errors in beat detection propagate to measure segmentation
- **Symptoms**: Misaligned measures, incorrect measure boundaries
- **Mitigation**: Use multiple validation methods and error correction
- **Solution**: Implement robust boundary adjustment and validation

### **Incomplete Measures**
- **Issue**: Songs may start or end with partial measures
- **Example**: Pickup beats, fade-out endings
- **Mitigation**: Handle partial measures as special cases
- **Solution**: Implement flexible measure completion and padding

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 5 & 8 Complete**: Beat alignment and note sequences
- **Required Files**:
  - `data\\processed\\phase5\\aligned_beats\\*.json`
  - `data\\processed\\phase9\\difficulty_patterns\\*.json`
- **Music Theory**: Understanding of time signatures and measure structure

### **What This Phase Unlocks**
- **Phase 11**: Measure structure enables special section identification
- **Phase 13**: Proper measure formatting for TJA chart assembly
- **Visual Organization**: Measures provide visual structure for chart reading
- **Playability**: Proper measure boundaries improve chart readability

### **Output Dependencies**
Subsequent phases depend on these Phase 10 outputs:
- `data\\processed\\phase10\\measure_segments\\*.json` - Measure boundary information
- `data\\processed\\phase10\\bar_lines\\*.json` - Bar line positions for TJA formatting
- `data\\processed\\phase10\\measure_validation\\*.json` - Segmentation quality metrics

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_measure_segmentation():
    """Test basic measure segmentation functionality."""
    # Create test data
    note_sequence = [
        {"time": 0.0, "note_type": "don"},
        {"time": 0.5, "note_type": "ka"},
        {"time": 1.0, "note_type": "don"},
        {"time": 1.5, "note_type": "rest"}
    ]
    
    aligned_beats = [
        {"beat_time": 0.0}, {"beat_time": 0.5}, 
        {"beat_time": 1.0}, {"beat_time": 1.5}
    ]
    
    measures, bar_lines = segment_into_measures(
        note_sequence, aligned_beats, 120.0, "4/4", 0.0
    )
    
    assert len(measures) >= 1
    assert len(bar_lines) >= 1
    assert measures[0]["time_signature"] == "4/4"

def test_time_signature_parsing():
    """Test time signature parsing."""
    beats, unit = parse_time_signature("4/4")
    assert beats == 4 and unit == 4
    
    beats, unit = parse_time_signature("3/4")
    assert beats == 3 and unit == 4
    
    beats, unit = parse_time_signature("6/8")
    assert beats == 6 and unit == 8
```

### **Quality Metrics**
- **Timing Accuracy**: >90% of measures within ±5% of expected duration
- **Measure Completeness**: >95% of measures contain at least one note
- **Boundary Alignment**: >85% of measure boundaries within ±50ms of beats
- **Processing Speed**: <30 seconds per song on RTX 3070

### **Musical Validation**
```python
def validate_musical_structure(measure_segments: List[Dict]) -> Dict:
    """Validate musical structure of segmented measures."""
    
    validation_metrics = {
        "measure_count_appropriateness": 0.0,
        "duration_consistency": 0.0,
        "complexity_distribution": 0.0,
        "structural_coherence": 0.0
    }
    
    if not measure_segments:
        return validation_metrics
    
    # Check measure count (typical songs have 32-128 measures)
    measure_count = len(measure_segments)
    if 32 <= measure_count <= 128:
        validation_metrics["measure_count_appropriateness"] = 1.0
    else:
        # Penalize very short or very long songs
        validation_metrics["measure_count_appropriateness"] = max(0.0, 1.0 - abs(measure_count - 64) / 64.0)
    
    # Check duration consistency
    durations = [m["duration"] for m in measure_segments]
    if durations:
        duration_cv = np.std(durations) / np.mean(durations)  # Coefficient of variation
        validation_metrics["duration_consistency"] = max(0.0, 1.0 - duration_cv)
    
    # Check complexity distribution
    complexities = [m["measure_complexity"] for m in measure_segments]
    if complexities:
        # Good charts have varied complexity
        complexity_variance = np.var(complexities)
        validation_metrics["complexity_distribution"] = min(1.0, complexity_variance * 4.0)
    
    # Overall structural coherence
    validation_metrics["structural_coherence"] = np.mean(list(validation_metrics.values()))
    
    return validation_metrics
```

### **Example Success Case**
```python
# Expected measure segmentation results
measure_segments = [
    {
        "measure_id": 0,
        "start_time": 0.0,
        "end_time": 2.0,
        "duration": 2.0,
        "time_signature": "4/4",
        "beat_count": 4,
        "subdivision_count": 16,
        "notes_in_measure": [
            {"time": 0.0, "note_type": "don"},
            {"time": 0.5, "note_type": "ka"},
            {"time": 1.0, "note_type": "don"},
            {"time": 1.5, "note_type": "ka"}
        ],
        "bar_line_position": 2.0,
        "measure_complexity": 0.75,
        "is_complete": True
    }
]

# Expected validation results
validation_results = {
    "total_measures": 64,
    "complete_measures": 62,
    "incomplete_measures": 2,
    "average_measure_duration": 2.0,
    "timing_accuracy": 0.92,
    "measure_consistency": 0.88,
    "validation_issues": []
}

# Expected processing results
processing_results = {
    "total_songs": 150,
    "processed_songs": 147,
    "total_measures": 9408,
    "average_measures_per_song": 64.0,
    "average_timing_accuracy": 0.89,
    "processing_errors": []
}
```

---

**Phase 10 Complete**. This phase segments generated note sequences into proper musical measures with accurate bar line positions, providing the structural framework needed for TJA chart formatting.

**Next**: [Phase 11: Go-Go Time & Special Sections](phase_11_special_sections.md)
