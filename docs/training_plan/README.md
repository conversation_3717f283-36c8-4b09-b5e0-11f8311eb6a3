# 🎼 TJA Chart Generation Training Pipeline - Master Plan

## 📋 Project Overview

This document outlines the complete incremental training plan for developing a machine learning system that generates high-difficulty TJA rhythm charts (levels 8-10) from audio data.

**Goal**: Transform `.ogg` audio files + metadata (BPM, Offset) → Valid `.tja` chart files

**Dataset**: 9 genre categories with hundreds of songs in `data\\raw\\ese\\`

**Reference Materials**:
- [TJA Format Specification](../references/tja_spec/TJA-format.mediawiki)
- [TJA Parser Implementation](../references/tja_parser/)

---

## 🗂️ Phase Organization by ML Pipeline Architecture

### **Phase 1-3: Audio Preprocessing & Feature Extraction**
| Phase | Name | Status | Est. Time | Dependencies |
|-------|------|--------|-----------|--------------|
| 1 | [Audio Loading & Format Standardization](phase_01_audio_loading.md) | ✅ Documented | 2 days | None |
| 2 | [Audio Quality Assessment & Filtering](phase_02_quality_assessment.md) | ✅ Documented | 1 day | Phase 1 |
| 3 | [Silence Detection & Audio Segmentation](phase_03_silence_detection.md) | ✅ Documented | 2 days | Phase 1, 2 |

### **Phase 4-6.5: Low-Level Label Learning & Feature Extraction**
| Phase | Name | Status | Est. Time | Dependencies |
|-------|------|--------|-----------|--------------|
| 4 | [Beat Position Estimation](phase_04_beat_estimation.md) | ✅ Documented | 3 days | Phase 1, 3 |
| 5 | [Tempo Alignment & BPM Validation](phase_05_tempo_alignment.md) | ✅ Documented | 2 days | Phase 4 |
| 6 | [Note Candidate Window Detection](phase_06_note_candidates.md) | ✅ Documented | 3 days | Phase 4, 5 |
| 6.5 | [Advanced Feature Extraction](phase_06_5_feature_extraction.md) | ✅ Documented | 3 days | Phase 6 |

### **Phase 7-9: Mid-Level Pattern Modeling**
| Phase | Name | Status | Est. Time | Dependencies |
|-------|------|--------|-----------|--------------|
| 7 | [Basic Note Type Classification](phase_07_note_classification.md) | ✅ Documented | 4 days | Phase 6.5 |
| 8 | [Note Sequence Pattern Learning](phase_08_sequence_patterns.md) | ✅ Documented | 5 days | Phase 7 |
| 9 | [Difficulty-Aware Pattern Modeling](phase_09_difficulty_patterns.md) | ✅ Documented | 4 days | Phase 8 |

### **Phase 10-11: Structural Learning**
| Phase | Name | Status | Est. Time | Dependencies |
|-------|------|--------|-----------|--------------|
| 10 | [Measure Segmentation & Bar Lines](phase_10_measure_segmentation.md) | ✅ Documented | 3 days | Phase 5, 8 |
| 11 | [Go-Go Time & Special Sections](phase_11_special_sections.md) | ✅ Documented | 3 days | Phase 10 |

### **Phase 12+: TJA Formatting & Final Assembly**
| Phase | Name | Status | Est. Time | Dependencies |
|-------|------|--------|-----------|--------------|
| 12 | [TJA Header Generation](phase_12_tja_headers.md) | ✅ Documented | 2 days | Phase 1 |
| 13 | [Chart Assembly & Formatting](phase_13_chart_assembly.md) | ✅ Documented | 3 days | Phase 9, 10, 11, 12 |
| 14 | [Validation & Quality Control](phase_14_validation.md) | ✅ Documented | 3 days | Phase 13 |
| 15 | [Local Deployment & CLI](phase_15_deployment.md) | ✅ Documented | 2 days | Phase 14 |

---

## 📊 Progress Tracking

### **Overall Progress**
- **Total Phases**: 16 (including Phase 6.5)
- **Documented**: 16 (100%) ✅
- **Ready for Implementation**: All 16 phases
- **Estimated Total Time**: 44 days
- **Documentation Complete**: Full training pipeline specified

### **Status Legend**
- ✅ **Documented**: Specification complete, ready for implementation (All 15 phases)
- 🔄 **In Progress**: Currently being implemented
- 📋 **Planned**: High-level design complete, needs detailed specification
- ⏸️ **Blocked**: Waiting for dependencies
- ❌ **Failed**: Needs revision or alternative approach

### **🎉 Documentation Complete!**
All 15 phases of the TJA chart generation training pipeline have been fully documented with comprehensive specifications, implementation plans, validation strategies, and locally-executable code examples.

---

## 🔄 Data Flow Overview

### **Phase Data Dependencies**
Each phase has clearly defined input/output specifications with validation requirements:

| **Phase** | **Input Source** | **Output Format** | **Next Phase Consumes** |
|-----------|------------------|-------------------|-------------------------|
| **Phase 4** | Phase 3 (silence maps, segments) | Beat positions, onset data | Phase 5 (tempo alignment) |
| **Phase 5** | Phase 4 (beat positions) | Aligned beats, BPM validation | Phase 6 (note candidates) |
| **Phase 6** | Phase 5 (aligned beats) | Note candidate windows | Phase 6.5 (feature extraction) |
| **Phase 6.5** | Phase 6 (candidates) | ML-ready features | Phase 7 (classification) |
| **Phase 7** | Phase 6.5 (features) | Classified notes | Phase 8 (sequence patterns) |
| **Phase 8** | Phase 7 (classified notes) | Note sequences | Phase 9 (difficulty patterns) |
| **Phase 9** | Phase 8 (sequences) | Difficulty patterns | Phase 10 (measure segmentation) |
| **Phase 10** | Phase 9 + Phase 5 | Measure segments, bar lines | Phase 11 (special sections) |
| **Phase 11** | Phase 10 (measures) | Go-Go sections, scroll changes | Phase 12 (TJA headers) |
| **Phase 12** | Phase 11 (special sections) | TJA headers | Phase 13 (chart assembly) |
| **Phase 13** | Phases 9,10,11,12 | Complete TJA charts | Phase 14 (validation) |
| **Phase 14** | Phase 13 (assembled charts) | Validated charts | Phase 15 (deployment) |
| **Phase 15** | Phase 14 + all models | Local deployment package | Production use |

### **Data Format Consistency**
- **File Naming**: `{song_name}_{data_type}.{extension}`
- **Directory Structure**: `data\\processed\\phase{NN}\\{category}\\`
- **Cross-References**: All candidate_ids and note_ids are traceable across phases
- **Validation**: Each phase includes input/output validation requirements

---

## 🔗 Phase Dependencies Graph

```mermaid
graph TD
    P1[Phase 1: Audio Loading] --> P2[Phase 2: Quality Assessment]
    P1 --> P3[Phase 3: Silence Detection]
    P2 --> P3
    P1 --> P4[Phase 4: Beat Estimation]
    P3 --> P4
    P4 --> P5[Phase 5: Tempo Alignment]
    P4 --> P6[Phase 6: Note Candidates]
    P5 --> P6
    P6 --> P6_5[Phase 6.5: Feature Extraction]
    P6_5 --> P7[Phase 7: Note Classification]
    P7 --> P8[Phase 8: Sequence Patterns]
    P8 --> P9[Phase 9: Difficulty Patterns]
    P5 --> P10[Phase 10: Measure Segmentation]
    P8 --> P10
    P10 --> P11[Phase 11: Special Sections]
    P1 --> P12[Phase 12: TJA Headers]
    P9 --> P13[Phase 13: Chart Assembly]
    P10 --> P13
    P11 --> P13
    P12 --> P13
    P13 --> P14[Phase 14: Validation]
    P14 --> P15[Phase 15: Local Deployment]
```

---

## 🎯 Quality Gates & Success Criteria

### **Phase-Level Quality Gates**
Each phase must meet these minimum requirements before proceeding:

1. **Success Rate**: >95% of test cases pass
2. **Quality Pass Rate**: >90% of outputs meet quality thresholds
3. **Performance**: Meets specified processing speed requirements
4. **Memory Usage**: Stays within RTX 3070 limits (≤8GB VRAM, ≤16GB RAM)
5. **Validation**: All unit tests pass with >90% coverage

### **Pipeline-Level Success Criteria**
- **Temporal Alignment Accuracy**: >80% of generated notes align within ±100ms of reference
- **Note Type Accuracy**: >85% correct classification for basic notes (don, ka, rest)
- **Chart Playability**: Generated charts pass TJA parser validation
- **Difficulty Consistency**: Generated charts match target difficulty level (8-10)

---

## 🛠️ Development Environment Requirements

### **Hardware Requirements**
- **GPU**: RTX 3070 (8GB VRAM)
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 100GB free space for datasets and models
- **OS**: Windows 10/11 with WSL2 support

### **Software Requirements**
```bash
# Core ML Environment
Python 3.12
CUDA 12.1
PyTorch 2.0+
librosa >= 0.9.0
numpy >= 1.21.0
pandas >= 1.3.0

# Audio Processing
soundfile >= 0.10.0
scipy >= 1.7.0
matplotlib >= 3.4.0

# Development Tools
pytest >= 6.0.0
black >= 21.0.0
jupyter >= 1.0.0
```

---

## 📁 Standardized Project Structure

```
D:\\TJAGen\\                                    # Project root
├── data\\                                      # All data storage
│   ├── raw\\                                   # Original input data
│   │   └── ese\\                               # ESE dataset
│   ├── processed\\                             # Phase processing outputs
│   │   ├── phase01\\                           # Phase 1 outputs
│   │   ├── phase02\\                           # Phase 2 outputs
│   │   ├── phase03\\                           # Phase 3 outputs
│   │   ├── phase04\\                           # Phase 4 outputs
│   │   ├── phase05\\                           # Phase 5 outputs
│   │   ├── phase06\\                           # Phase 6 outputs
│   │   ├── phase06_5\\                         # Phase 6.5 outputs
│   │   ├── phase07\\                           # Phase 7 outputs
│   │   ├── phase08\\                           # Phase 8 outputs
│   │   ├── phase09\\                           # Phase 9 outputs
│   │   ├── phase10\\                           # Phase 10 outputs
│   │   ├── phase11\\                           # Phase 11 outputs
│   │   ├── phase12\\                           # Phase 12 outputs
│   │   ├── phase13\\                           # Phase 13 outputs
│   │   ├── phase14\\                           # Phase 14 outputs
│   │   └── phase15\\                           # Phase 15 outputs
│   ├── models\\                                # All trained models
│   │   ├── phase07\\                           # Note classification models
│   │   ├── phase08\\                           # Sequence pattern models
│   │   └── phase09\\                           # Difficulty modeling models
│   ├── logs\\                                  # All logging outputs
│   ├── tests\\                                 # All test files and data
│   ├── config\\                                # All configuration files
│   └── temp\\                                  # Temporary and cache files
├── src\\                                       # Source code
│   ├── phases\\                                # Phase implementation modules
│   ├── utils\\                                 # Utility modules
│   └── tests\\                                 # Test source code
├── docs\\                                      # Documentation
│   ├── training_plan\\                         # Phase documentation
│   └── references\\                            # TJA specs and parsers
├── scripts\\                                   # Execution scripts
└── dist\\                                      # Local deployment package
```

### **File Naming Conventions**
- **Data Files**: `{song_name}_{data_type}.{extension}`
- **Model Files**: `{model_name}_phase{N}.pth`
- **Log Files**: `phase{N:02d}_{YYYYMMDD}_{HHMMSS}.log`
- **Configuration**: `phase{N:02d}_config.json`

---

## 🔄 Data Flow Overview

### **Phase Data Dependencies**
Each phase has clearly defined input/output specifications with validation requirements:

| **Phase** | **Input Source** | **Output Format** | **Next Phase Consumes** |
|-----------|------------------|-------------------|-------------------------|
| **Phase 4** | Phase 3 (silence maps, segments) | Beat positions, onset data | Phase 5 (tempo alignment) |
| **Phase 5** | Phase 4 (beat positions) | Aligned beats, BPM validation | Phase 6 (note candidates) |
| **Phase 6** | Phase 5 (aligned beats) | Note candidate windows | Phase 6.5 (feature extraction) |
| **Phase 6.5** | Phase 6 (candidates) | ML-ready features | Phase 7 (classification) |
| **Phase 7** | Phase 6.5 (features) | Classified notes | Phase 8 (sequence patterns) |
| **Phase 8** | Phase 7 (classified notes) | Note sequences | Phase 9 (difficulty patterns) |
| **Phase 9** | Phase 8 (sequences) | Difficulty patterns | Phase 10 (measure segmentation) |
| **Phase 10** | Phase 9 + Phase 5 | Measure segments, bar lines | Phase 11 (special sections) |
| **Phase 11** | Phase 10 (measures) | Go-Go sections, scroll changes | Phase 12 (TJA headers) |
| **Phase 12** | Phase 11 (special sections) | TJA headers | Phase 13 (chart assembly) |
| **Phase 13** | Phases 9,10,11,12 | Complete TJA charts | Phase 14 (validation) |
| **Phase 14** | Phase 13 (assembled charts) | Validated charts | Phase 15 (deployment) |
| **Phase 15** | Phase 14 + all models | Local deployment package | Production use |

### **Data Format Consistency**
- **File Naming**: `{song_name}_{data_type}.{extension}`
- **Directory Structure**: `data\\processed\\phase{NN}\\{category}\\`
- **Cross-References**: All candidate_ids and note_ids are traceable across phases
- **Validation**: Each phase includes input/output validation requirements

---

## 🚀 Getting Started

1. **Review Phase 1**: Start with [Audio Loading & Format Standardization](phase_01_audio_loading.md)
2. **Set up Environment**: Install required dependencies
3. **Validate Dataset**: Ensure `data\\raw\\ese\\` contains expected structure
4. **Run Phase 1**: Execute audio loading pipeline
5. **Validate Results**: Check Phase 1 outputs before proceeding

---

## 📞 Support & References

- **TJA Format Questions**: See [TJA Format Specification](../references/tja_spec/TJA-format.mediawiki)
- **Parser Issues**: Reference [TJA Parser Implementation](../references/tja_parser/)
- **Phase-Specific Help**: Check individual phase documentation
- **Technical Issues**: Review validation strategies in each phase

---

## 📋 Documentation Improvements Summary

### **Enhanced Data Flow Documentation**
- **Clear Input/Output Specifications**: Each phase now has detailed data format specifications
- **Cross-Phase Compatibility**: Explicit format matching between producing and consuming phases
- **Validation Requirements**: Input and output validation criteria for each phase
- **File Naming Consistency**: Standardized naming conventions across all phases

### **Consolidated Content**
- **Integrated Path Standards**: Path standardization information moved into individual phase files
- **Removed Redundancy**: Eliminated duplicate documentation files
- **Streamlined Structure**: Focused on essential phase documentation only
- **Improved Cross-References**: Better linking between dependent phases

### **Technical Accuracy**
- **Windows-Compatible Paths**: All paths use Windows-style backslashes
- **RTX 3070 Optimization**: Memory and performance constraints clearly specified
- **Phase 6.5 Integration**: Proper integration of advanced feature extraction phase
- **Data Format Precision**: Exact data structures and array shapes specified

---

*Last Updated: 2025-07-26*
*Total Estimated Duration: 44 days*
*Documentation Status: Streamlined and Enhanced*
*Current Phase: Ready for Implementation*
