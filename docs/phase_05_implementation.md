# Phase 5: Tempo Alignment & BPM Validation - Implementation Guide

## Overview

Phase 5 implements tempo alignment and BPM validation for the TJA chart generation ML training pipeline. This phase takes detected beats from Phase 4 and aligns them with the expected BPM from TJA files, providing the precise timing foundation needed for accurate note generation.

## Key Features

- **Tempo Alignment**: Aligns detected beats with TJA BPM references using optimization algorithms
- **BPM Validation**: Validates tempo consistency against TJA metadata with configurable tolerance
- **Tempo Change Detection**: Identifies tempo changes within songs using sliding window analysis
- **Robust Error Handling**: Comprehensive error handling and logging for production use
- **Phase 6 Compatibility**: Outputs aligned beats in format compatible with subsequent phases

## Architecture

### Core Components

1. **TempoAlignmentProcessor**: Main processing class
2. **Configuration System**: YAML-based configuration with sensible defaults
3. **Visualization Tools**: Optional plotting and analysis utilities
4. **Comprehensive Testing**: Unit tests and integration tests

### Data Flow

```
Phase 4 Beat Positions → Tempo Alignment → BPM Validation → Aligned Beats → Phase 6
                    ↗                                    ↘
              TJA Metadata                        Validation Reports
```

## File Structure

```
src/phases/phase_05_tempo_alignment.py    # Main implementation
configs/phase_05_config.yaml             # Configuration file
scripts/run_phase_05.py                  # Execution script
src/tests/test_phase_05.py               # Unit tests
src/utils/phase_05_visualizer.py         # Visualization utilities
scripts/test_phase_05_integration.py     # Integration tests
```

## Usage

### Basic Usage

```python
from phases.phase_05_tempo_alignment import run_phase_05

# Run with default parameters
results = run_phase_05()

# Run with custom parameters
results = run_phase_05(
    input_dir="data/processed/phase4/outputs",
    output_dir="data/processed/phase5",
    tja_data_dir="data/raw/ese",
    bpm_tolerance=0.05  # 5% tolerance
)
```

### Command Line Usage

```bash
# Basic execution
python scripts/run_phase_05.py

# With custom configuration
python scripts/run_phase_05.py --config configs/phase_05_config.yaml

# With custom tolerance
python scripts/run_phase_05.py --tolerance 0.03

# With verbose output
python scripts/run_phase_05.py --verbose
```

### Advanced Usage

```python
from phases.phase_05_tempo_alignment import TempoAlignmentProcessor

# Initialize processor
processor = TempoAlignmentProcessor(
    input_dir="data/processed/phase4/outputs",
    output_dir="data/processed/phase5",
    tja_data_dir="data/raw/ese",
    bpm_tolerance=0.05
)

# Process single song
beat_positions = [...]  # Load beat positions
tja_bpm = 120.0
alignment_result = processor.align_tempo_with_tja(beat_positions, tja_bpm, 0.0)

# Validate BPM
validation_result = processor.validate_bpm_alignment(122.0, 120.0, [121.0, 122.0])

# Process entire dataset
results = processor.process_tempo_alignment()
```

## Configuration

The configuration file `configs/phase_05_config.yaml` contains all adjustable parameters:

### Key Parameters

- **bpm_tolerance**: Acceptable BPM error percentage (default: 0.05 = 5%)
- **tempo_change_threshold**: Threshold for detecting tempo changes (default: 0.1 = 10%)
- **validation_pass_threshold**: Minimum validation pass rate (default: 85%)
- **create_visualizations**: Enable/disable visualization generation

### Example Configuration

```yaml
# Tempo Alignment Parameters
bpm_tolerance: 0.05
phase_optimization_points: 50
tempo_change_threshold: 0.1

# Validation Thresholds
max_alignment_error_ms: 100
min_confidence_threshold: 0.7

# Processing Options
create_visualizations: true
save_timing_analysis: true
```

## Input/Output Formats

### Input (from Phase 4)

Beat position files in `data/processed/phase4/outputs/beat_positions/`:

```json
{
  "song_name": "SongName",
  "segment_id": 0,
  "tempo": 120.5,
  "beats": [
    {
      "time": 0.5,
      "confidence": 0.85,
      "strength": 0.9,
      "beat_number": 0
    }
  ]
}
```

### TJA Metadata

TJA files in `data/raw/ese/` containing:

```
TITLE:Song Name
BPM:120.0
OFFSET:-1.5
```

### Output

#### Tempo Alignment Results (`tempo_alignment/`)

```json
{
  "aligned_bpm": 120.0,
  "bpm_confidence": 0.92,
  "tempo_drift": 2.1,
  "alignment_offset": 0.05,
  "beat_grid": [
    {
      "beat_time": 0.5,
      "original_time": 0.48,
      "correction": 0.02,
      "grid_position": 0,
      "confidence": 0.85
    }
  ],
  "tempo_changes": []
}
```

#### BPM Validation Results (`bpm_validation/`)

```json
{
  "tja_bpm": 120.0,
  "detected_bpm": 121.5,
  "bpm_error": 1.5,
  "bpm_error_percentage": 1.25,
  "validation_passed": true,
  "validation_threshold": 5.0,
  "segment_consistency": 0.95
}
```

#### Aligned Beats (`aligned_beats/`)

Phase 6 compatible format:

```json
[
  {
    "beat_id": 0,
    "beat_time": 0.5,
    "original_time": 0.48,
    "correction": 0.02,
    "confidence": 0.85,
    "is_downbeat": true,
    "measure_position": 0,
    "bpm_at_beat": 120.0
  }
]
```

## Algorithms

### Tempo Alignment

1. **BPM Estimation**: Calculate BPM from detected beat intervals
2. **Error Assessment**: Compare detected BPM with TJA reference
3. **Phase Optimization**: Find optimal phase offset using cost function
4. **Grid Generation**: Create aligned beat grid at target BPM
5. **Correction Application**: Apply timing corrections to detected beats

### BPM Validation

1. **Error Calculation**: Compute percentage error between detected and reference BPM
2. **Threshold Comparison**: Check against configurable tolerance
3. **Consistency Analysis**: Evaluate consistency across segments
4. **Pass/Fail Decision**: Determine validation result

### Tempo Change Detection

1. **Sliding Window**: Analyze local BPM in overlapping windows
2. **Change Detection**: Identify significant BPM deviations
3. **Confidence Scoring**: Assign confidence based on change magnitude

## Testing

### Unit Tests

```bash
# Run all tests
python -m pytest src/tests/test_phase_05.py -v

# Run specific test
python -m pytest src/tests/test_phase_05.py::TestTempoAlignment::test_tempo_alignment_perfect_match -v
```

### Integration Tests

```bash
# Run integration tests
python scripts/test_phase_05_integration.py
```

### Test Coverage

- Tempo alignment with perfect matches
- Tempo alignment with BPM mismatches
- Tempo alignment with noisy data
- BPM validation (pass/fail cases)
- Tempo change detection
- TJA metadata loading
- Error handling for edge cases

## Performance

### Typical Performance

- **Processing Speed**: ~50-100 songs/second
- **Memory Usage**: ~100-200MB for typical datasets
- **Accuracy**: >95% validation pass rate with 5% tolerance

### Optimization Tips

1. **Batch Processing**: Process multiple songs in batches
2. **Memory Management**: Clear cache periodically for large datasets
3. **Parallel Processing**: Can be parallelized by song
4. **Configuration Tuning**: Adjust tolerance based on data quality

## Troubleshooting

### Common Issues

1. **No TJA files found**: Check `tja_data_dir` path and file structure
2. **High validation failure rate**: Adjust `bpm_tolerance` or check beat detection quality
3. **Memory issues**: Reduce `batch_size` or enable `clear_cache_interval`
4. **JSON serialization errors**: Ensure all numpy types are properly converted

### Debug Mode

Enable verbose logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Validation Reports

Check `alignment_report.json` for comprehensive statistics:

```json
{
  "total_songs": 100,
  "processed_songs": 98,
  "validation_passed": 92,
  "validation_failed": 6,
  "avg_bpm_error": 2.1,
  "avg_tempo_drift": 1.8,
  "processing_time": 2.5
}
```

## Integration with Other Phases

### Phase 4 Dependencies

- Requires beat position files from Phase 4
- Expects specific JSON format with beat times and confidences

### Phase 6 Compatibility

- Outputs aligned beats in Phase 6 compatible format
- Includes all required fields for note generation
- Maintains beat timing precision for accurate chart generation

## Future Enhancements

1. **Adaptive Tolerance**: Automatically adjust tolerance based on song characteristics
2. **Multi-BPM Support**: Handle songs with multiple BPM sections
3. **Advanced Tempo Modeling**: Use more sophisticated tempo models
4. **Real-time Processing**: Optimize for real-time tempo alignment
5. **Machine Learning Integration**: Use ML models for tempo prediction
