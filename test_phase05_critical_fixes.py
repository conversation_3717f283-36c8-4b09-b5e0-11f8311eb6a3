#!/usr/bin/env python3
"""
Test script to verify Phase 5 critical fixes are working correctly.
"""

import sys
import numpy as np
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from phases.phase_05_tempo_alignment import TempoAlignmentProcessor

def test_harmonic_confusion_resolution():
    """Test harmonic confusion resolution."""
    print("Testing harmonic confusion resolution...")
    
    processor = TempoAlignmentProcessor(
        input_dir=Path("data/processed/phase4/outputs"),
        output_dir=Path("data/processed/phase5_test"),
        tja_data_dir=Path("data/raw/ese"),
        bpm_tolerance=0.05
    )
    
    # Test cases: (detected_bpm, reference_bpm, expected_correction)
    test_cases = [
        (90.0, 180.0, 180.0),    # Half tempo -> should correct to 2x
        (360.0, 180.0, 180.0),   # Double tempo -> should correct to 0.5x
        (120.0, 120.0, 120.0),   # Correct tempo -> no change
        (60.0, 120.0, 120.0),    # Half tempo -> should correct to 2x
        (240.0, 120.0, 120.0),   # Double tempo -> should correct to 0.5x
    ]
    
    passed = 0
    for detected, reference, expected in test_cases:
        corrected, error = processor.resolve_harmonic_confusion(detected, reference)
        
        # Allow small tolerance for floating point comparison
        if abs(corrected - expected) < 1.0:
            print(f"✅ {detected} BPM -> {corrected} BPM (expected {expected})")
            passed += 1
        else:
            print(f"❌ {detected} BPM -> {corrected} BPM (expected {expected})")
    
    print(f"Harmonic confusion resolution: {passed}/{len(test_cases)} tests passed")
    return passed == len(test_cases)

def test_adaptive_tolerance():
    """Test adaptive tolerance calculation."""
    print("\nTesting adaptive tolerance...")
    
    processor = TempoAlignmentProcessor(
        input_dir=Path("data/processed/phase4/outputs"),
        output_dir=Path("data/processed/phase5_test"),
        tja_data_dir=Path("data/raw/ese"),
        bpm_tolerance=0.05
    )
    
    # Test cases: (song_name, expected_multiplier_range)
    test_cases = [
        ("Normal Song", (1.0, 1.0)),           # Normal song
        ("Classical Medley", (1.3, 1.5)),     # Should get higher tolerance
        ("Jazz Remix", (1.2, 1.5)),           # Should get higher tolerance
        ("Live Concert Version", (1.4, 1.4)), # Should get higher tolerance
        ("Symphony No. 5", (1.3, 1.3)),       # Classical should get higher tolerance
    ]
    
    base_tolerance = 0.05
    passed = 0
    
    for song_name, (min_mult, max_mult) in test_cases:
        tolerance = processor.get_adaptive_tolerance(song_name, base_tolerance)
        actual_mult = tolerance / base_tolerance
        
        if min_mult <= actual_mult <= max_mult:
            print(f"✅ '{song_name}': {tolerance:.3f} (multiplier: {actual_mult:.1f})")
            passed += 1
        else:
            print(f"❌ '{song_name}': {tolerance:.3f} (multiplier: {actual_mult:.1f}, expected: {min_mult}-{max_mult})")
    
    print(f"Adaptive tolerance: {passed}/{len(test_cases)} tests passed")
    return passed == len(test_cases)

def test_fallback_bpm_estimation():
    """Test fallback BPM estimation."""
    print("\nTesting fallback BPM estimation...")
    
    processor = TempoAlignmentProcessor(
        input_dir=Path("data/processed/phase4/outputs"),
        output_dir=Path("data/processed/phase5_test"),
        tja_data_dir=Path("data/raw/ese"),
        bpm_tolerance=0.05
    )
    
    # Find some actual beat files to test with
    beat_dir = Path("data/processed/phase4/outputs/beat_positions")
    beat_files = list(beat_dir.glob("*.json"))
    
    if len(beat_files) > 0:
        # Test with first available song
        test_files = beat_files[:3]  # Use first 3 files
        song_name = "Test Song"
        
        estimated_bpm = processor.estimate_fallback_bpm(song_name, test_files)
        
        if estimated_bpm is not None and 60 <= estimated_bpm <= 200:
            print(f"✅ Fallback BPM estimation: {estimated_bpm:.1f} BPM (reasonable range)")
            return True
        else:
            print(f"❌ Fallback BPM estimation: {estimated_bpm} BPM (unreasonable)")
            return False
    else:
        print("⚠️  No beat files found for fallback BPM testing")
        return True  # Skip test if no files available

def test_enhanced_validation():
    """Test enhanced validation with all fixes."""
    print("\nTesting enhanced validation...")
    
    processor = TempoAlignmentProcessor(
        input_dir=Path("data/processed/phase4/outputs"),
        output_dir=Path("data/processed/phase5_test"),
        tja_data_dir=Path("data/raw/ese"),
        bpm_tolerance=0.05
    )
    
    # Test validation with harmonic confusion case
    detected_bpm = 90.0  # Half tempo
    tja_bpm = 180.0      # Actual tempo
    segment_bpms = [88.0, 92.0, 89.0]  # Consistent half tempo
    song_name = "Test Classical Medley"  # Should get adaptive tolerance
    
    result = processor.validate_bpm_alignment(detected_bpm, tja_bpm, segment_bpms, song_name)
    
    # Check if harmonic correction was applied
    if result.get("harmonic_correction_applied", False):
        print(f"✅ Harmonic correction applied: {detected_bpm} -> {result['corrected_bpm']}")
    else:
        print(f"❌ Harmonic correction not applied")
        return False
    
    # Check if validation passed after correction
    if result.get("validation_passed", False):
        print(f"✅ Validation passed after correction (error: {result['bpm_error_percentage']:.1f}%)")
    else:
        print(f"❌ Validation failed after correction (error: {result['bpm_error_percentage']:.1f}%)")
        return False
    
    # Check adaptive tolerance
    if result.get("validation_threshold", 5.0) > 5.0:
        print(f"✅ Adaptive tolerance applied: {result['validation_threshold']:.1f}%")
    else:
        print(f"⚠️  Adaptive tolerance not applied: {result['validation_threshold']:.1f}%")
    
    return True

def main():
    """Run all tests."""
    print("=" * 60)
    print("Phase 5 Critical Fixes Verification")
    print("=" * 60)
    
    tests = [
        test_harmonic_confusion_resolution,
        test_adaptive_tolerance,
        test_fallback_bpm_estimation,
        test_enhanced_validation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()
    
    print("=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All critical fixes verified successfully!")
        print("Ready to run Phase 5 with improvements!")
        return 0
    else:
        print("⚠️  Some fixes need attention before running Phase 5.")
        return 1

if __name__ == "__main__":
    exit(main())
