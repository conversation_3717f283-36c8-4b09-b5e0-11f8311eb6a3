#!/usr/bin/env python3
"""Test Phase 5 with a small subset of songs."""

import sys
sys.path.append('src')
from phases.phase_05_tempo_alignment import TempoAlignmentProcessor
from pathlib import Path
import json
from tqdm import tqdm

def main():
    # Test with first 10 songs only
    processor = TempoAlignmentProcessor(
        input_dir=Path('data/processed/phase4/outputs'),
        output_dir=Path('data/processed/phase5_test'),
        tja_data_dir=Path('data/raw/ese'),
        bpm_tolerance=0.05
    )

    # Find beat files
    beat_files = list((processor.input_dir / 'beat_positions').glob('*.json'))
    songs = {}
    for beat_file in beat_files:
        song_name = beat_file.stem.split('_segment_')[0]
        if song_name not in songs:
            songs[song_name] = []
        songs[song_name].append(beat_file)

    # Process first 10 songs
    test_songs = list(songs.items())[:10]
    print(f'Testing with {len(test_songs)} songs')

    success_count = 0
    for song_name, song_beat_files in tqdm(test_songs, desc='Testing'):
        result = processor.process_single_song(song_name, song_beat_files)
        if 'error' not in result:
            success_count += 1
        else:
            print(f'Error in {song_name}: {result["error"]}')

    print(f'Successfully processed {success_count}/{len(test_songs)} songs')

if __name__ == "__main__":
    main()
