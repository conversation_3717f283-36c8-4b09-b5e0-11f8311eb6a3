#!/usr/bin/env python3
"""
Test script to verify Phase 5 fixes for division by zero and missing TJA files.
"""

import sys
import numpy as np
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent / "src"))

from phases.phase_05_tempo_alignment import TempoAlignmentProcessor

def test_division_by_zero_fix():
    """Test that division by zero is handled properly."""
    print("Testing division by zero fix...")
    
    processor = TempoAlignmentProcessor(
        input_dir=Path("data/processed/phase4/outputs"),
        output_dir=Path("data/processed/phase5_test"),
        tja_data_dir=Path("data/raw/ese"),
        bpm_tolerance=0.05
    )
    
    # Create test data with zero intervals (problematic case)
    beat_times = np.array([0.0, 0.0, 0.0, 1.0, 2.0])  # Some zero intervals
    base_bpm = 120.0
    
    try:
        # This should not raise a division by zero error
        tempo_changes = processor.detect_tempo_changes(beat_times, base_bpm, window_size=3)
        print(f"✅ Division by zero fix works. Found {len(tempo_changes)} tempo changes.")
        return True
    except Exception as e:
        print(f"❌ Division by zero fix failed: {e}")
        return False

def test_tja_file_matching():
    """Test improved TJA file matching."""
    print("Testing TJA file matching...")
    
    processor = TempoAlignmentProcessor(
        input_dir=Path("data/processed/phase4/outputs"),
        output_dir=Path("data/processed/phase5_test"),
        tja_data_dir=Path("data/raw/ese"),
        bpm_tolerance=0.05
    )
    
    # Test the problematic song
    test_songs = [
        "365 Nichi no Love Story",  # This was failing before
        "!!!Chaos Time!!!",        # This should work
        "NonExistentSong"          # This should fail gracefully
    ]
    
    results = []
    for song in test_songs:
        bpm, offset = processor.load_tja_metadata(song)
        if bpm is not None:
            print(f"✅ Found TJA for '{song}': BPM={bpm}, Offset={offset}")
            results.append(True)
        else:
            print(f"⚠️  No TJA found for '{song}' (expected for NonExistentSong)")
            results.append(song == "NonExistentSong")  # Expected failure
    
    return all(results)

def test_caching():
    """Test TJA metadata caching."""
    print("Testing TJA metadata caching...")
    
    processor = TempoAlignmentProcessor(
        input_dir=Path("data/processed/phase4/outputs"),
        output_dir=Path("data/processed/phase5_test"),
        tja_data_dir=Path("data/raw/ese"),
        bpm_tolerance=0.05
    )
    
    song = "!!!Chaos Time!!!"
    
    # First call - should load from file
    bpm1, offset1 = processor.load_tja_metadata(song)
    
    # Second call - should load from cache
    bpm2, offset2 = processor.load_tja_metadata(song)
    
    if bpm1 == bpm2 and offset1 == offset2 and song in processor.tja_cache:
        print(f"✅ Caching works. Song '{song}' cached with BPM={bpm1}")
        return True
    else:
        print(f"❌ Caching failed for '{song}'")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("Phase 5 Fixes Verification")
    print("=" * 60)
    
    tests = [
        test_division_by_zero_fix,
        test_tja_file_matching,
        test_caching
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
        print()
    
    print("=" * 60)
    print("Test Results Summary")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test, result) in enumerate(zip(tests, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{i+1}. {test.__name__}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes verified successfully!")
        return 0
    else:
        print("⚠️  Some fixes need attention.")
        return 1

if __name__ == "__main__":
    exit(main())
