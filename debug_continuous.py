import numpy as np
import sys
sys.path.append('src')
from utils.silence_utils import detect_silence_regions

# Create continuous music (5s)
sr = 22050
audio = 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, 5, 5*sr))

print(f'Audio shape: {audio.shape}')
print(f'Audio max: {np.max(np.abs(audio)):.3f}')
print(f'Audio min: {np.min(np.abs(audio)):.3f}')

# Test silence detection
result = detect_silence_regions(
    audio, sr,
    silence_threshold=-70,
    adaptive_threshold=False,
    use_multiband=False
)

print(f'Silence regions: {len(result["silence_regions"])}')
print(f'Music regions: {len(result["music_regions"])}')
print(f'Silence %: {result["silence_percentage"]:.1f}%')
print(f'Music %: {result["music_percentage"]:.1f}%')

# Try with different threshold
result2 = detect_silence_regions(
    audio, sr,
    silence_threshold=-20,
    adaptive_threshold=False,
    use_multiband=False
)

print(f'\nWith -20dB threshold:')
print(f'Silence regions: {len(result2["silence_regions"])}')
print(f'Music regions: {len(result2["music_regions"])}')
print(f'Silence %: {result2["silence_percentage"]:.1f}%')
print(f'Music %: {result2["music_percentage"]:.1f}%')

# Check RMS energy
import librosa
rms_energy = librosa.feature.rms(y=audio, frame_length=2048, hop_length=512)[0]
rms_db = librosa.amplitude_to_db(rms_energy, ref=np.max)
print(f'\nRMS dB range: {np.min(rms_db):.1f} to {np.max(rms_db):.1f}')
print(f'RMS dB mean: {np.mean(rms_db):.1f}')
