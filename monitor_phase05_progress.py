#!/usr/bin/env python3
"""
Monitor Phase 5 processing progress and analyze results.
"""

import json
import time
from pathlib import Path
import numpy as np

def analyze_current_results():
    """Analyze current Phase 5 results."""
    validation_dir = Path('data/processed/phase5/bpm_validation')
    
    if not validation_dir.exists():
        print("Phase 5 validation directory not found")
        return None
    
    validation_files = list(validation_dir.glob('*.json'))
    
    if not validation_files:
        print("No validation files found")
        return None
    
    passed = 0
    failed = 0
    bpm_errors = []
    harmonic_corrections = 0
    fallback_used = 0
    adaptive_tolerance_used = 0
    
    for file in validation_files:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if data['validation_passed']:
                passed += 1
            else:
                failed += 1
                
            bpm_errors.append(data['bpm_error_percentage'])
            
            # Check for new features
            if data.get('harmonic_correction_applied', False):
                harmonic_corrections += 1
                
            if data.get('fallback_bpm_used', False):
                fallback_used += 1
                
            # Check if adaptive tolerance was used (threshold > 5.0%)
            if data.get('validation_threshold', 5.0) > 5.0:
                adaptive_tolerance_used += 1
            
        except Exception as e:
            print(f'Error reading {file}: {e}')
            continue
    
    total = passed + failed
    if total == 0:
        return None
    
    results = {
        'total_processed': total,
        'passed': passed,
        'failed': failed,
        'pass_rate': passed / total * 100,
        'avg_bpm_error': np.mean(bpm_errors),
        'median_bpm_error': np.median(bpm_errors),
        'max_bpm_error': np.max(bpm_errors),
        'min_bpm_error': np.min(bpm_errors),
        'harmonic_corrections': harmonic_corrections,
        'harmonic_correction_rate': harmonic_corrections / total * 100,
        'fallback_used': fallback_used,
        'fallback_rate': fallback_used / total * 100,
        'adaptive_tolerance_used': adaptive_tolerance_used,
        'adaptive_tolerance_rate': adaptive_tolerance_used / total * 100,
        'high_error_count': len([e for e in bpm_errors if e > 20]),
        'high_error_rate': len([e for e in bpm_errors if e > 20]) / len(bpm_errors) * 100
    }
    
    return results

def print_results(results):
    """Print formatted results."""
    if not results:
        print("No results to display")
        return
    
    print("=" * 60)
    print("Phase 5 Processing Results")
    print("=" * 60)
    print(f"Total Processed: {results['total_processed']:,} songs")
    print(f"Validation Pass Rate: {results['pass_rate']:.1f}% ({results['passed']}/{results['total_processed']})")
    print(f"Validation Fail Rate: {100-results['pass_rate']:.1f}% ({results['failed']}/{results['total_processed']})")
    print()
    
    print("BPM Error Statistics:")
    print(f"  Average BPM Error: {results['avg_bpm_error']:.2f}%")
    print(f"  Median BPM Error: {results['median_bpm_error']:.2f}%")
    print(f"  Error Range: {results['min_bpm_error']:.2f}% - {results['max_bpm_error']:.2f}%")
    print(f"  Songs with >20% error: {results['high_error_count']} ({results['high_error_rate']:.1f}%)")
    print()
    
    print("Critical Fixes Impact:")
    print(f"  Harmonic Corrections Applied: {results['harmonic_corrections']} ({results['harmonic_correction_rate']:.1f}%)")
    print(f"  Adaptive Tolerance Used: {results['adaptive_tolerance_used']} ({results['adaptive_tolerance_rate']:.1f}%)")
    print(f"  Fallback BPM Used: {results['fallback_used']} ({results['fallback_rate']:.1f}%)")
    print()
    
    # Compare with previous results
    print("Improvement vs Previous Results:")
    print(f"  Pass Rate: {results['pass_rate']:.1f}% (vs 34.7% previous) = +{results['pass_rate']-34.7:.1f}%")
    print(f"  Avg BPM Error: {results['avg_bpm_error']:.2f}% (vs 21.75% previous) = {(21.75-results['avg_bpm_error'])/21.75*100:.1f}% reduction")
    print()

def main():
    """Main monitoring function."""
    print("Monitoring Phase 5 processing...")
    
    while True:
        results = analyze_current_results()
        
        if results:
            print(f"\n[{time.strftime('%H:%M:%S')}] Current Progress:")
            print(f"Processed: {results['total_processed']:,} songs")
            print(f"Pass Rate: {results['pass_rate']:.1f}%")
            print(f"Avg BPM Error: {results['avg_bpm_error']:.2f}%")
            print(f"Harmonic Corrections: {results['harmonic_correction_rate']:.1f}%")
            
            # Check if processing is complete (approximately 2440 songs expected)
            if results['total_processed'] >= 2400:
                print("\n🎉 Processing appears to be complete!")
                print_results(results)
                break
        else:
            print(f"[{time.strftime('%H:%M:%S')}] No results found yet...")
        
        time.sleep(30)  # Check every 30 seconds

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user")
        results = analyze_current_results()
        if results:
            print_results(results)
