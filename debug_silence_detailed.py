import numpy as np
import sys
sys.path.append('src')
import librosa

# Create test audio: 2s music + 1s silence + 2s music
sr = 22050
music1 = 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, 2, 2*sr))
silence = np.zeros(int(1*sr))
music2 = 0.5 * np.sin(2 * np.pi * 880 * np.linspace(0, 2, 2*sr))
audio = np.concatenate([music1, silence, music2])

print(f'Audio shape: {audio.shape}')
print(f'Audio duration: {len(audio)/sr:.1f}s')

# Manual silence detection to debug
frame_length = 2048
hop_length = 512

# Calculate RMS energy
rms_energy = librosa.feature.rms(y=audio, frame_length=frame_length, hop_length=hop_length)[0]
rms_db = librosa.amplitude_to_db(rms_energy, ref=np.max)

# Create silence mask
silence_threshold = -30
silence_mask = rms_db < silence_threshold

# Also check for true silence
true_silence_mask = rms_db <= -70
combined_mask = silence_mask | true_silence_mask

print(f'RMS energy shape: {rms_energy.shape}')
print(f'Silence mask sum: {np.sum(silence_mask)} / {len(silence_mask)}')
print(f'True silence mask sum: {np.sum(true_silence_mask)} / {len(true_silence_mask)}')
print(f'Combined mask sum: {np.sum(combined_mask)} / {len(combined_mask)}')

# Convert frame indices to time
frame_times = librosa.frames_to_time(np.arange(len(combined_mask)), sr=sr, hop_length=hop_length)

# Find contiguous silence regions
silence_starts = np.where(np.diff(np.concatenate(([False], combined_mask))))[0]
silence_ends = np.where(np.diff(np.concatenate((combined_mask, [False]))))[0]

print(f'\nRaw silence regions found: {len(silence_starts)}')
for start_idx, end_idx in zip(silence_starts, silence_ends):
    start_time = frame_times[start_idx]
    end_time = frame_times[min(end_idx, len(frame_times)-1)]
    duration = end_time - start_time
    print(f'  Raw silence: {start_time:.2f}s - {end_time:.2f}s (duration: {duration:.2f}s)')

# Apply minimum duration filter
min_silence_duration = 0.5
filtered_regions = []
for start_idx, end_idx in zip(silence_starts, silence_ends):
    start_time = frame_times[start_idx]
    end_time = frame_times[min(end_idx, len(frame_times)-1)]
    duration = end_time - start_time
    if duration >= min_silence_duration:
        filtered_regions.append((start_time, end_time, duration))

print(f'\nFiltered silence regions (min {min_silence_duration}s): {len(filtered_regions)}')
for start_time, end_time, duration in filtered_regions:
    print(f'  Filtered silence: {start_time:.2f}s - {end_time:.2f}s (duration: {duration:.2f}s)')

# Check specific frames around expected silence
expected_silence_start = int(2.0 * sr / hop_length)
expected_silence_end = int(3.0 * sr / hop_length)
print(f'\nExpected silence frames: {expected_silence_start} to {expected_silence_end}')
print(f'Silence mask in expected region: {combined_mask[expected_silence_start:expected_silence_end]}')
print(f'RMS dB in expected region: {rms_db[expected_silence_start:expected_silence_end]}')
