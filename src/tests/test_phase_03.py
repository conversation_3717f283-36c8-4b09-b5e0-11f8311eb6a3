"""
Unit Tests for Phase 3: Silence Detection & Audio Segmentation
Tests the silence detection algorithms and audio segmentation functionality.
"""

import unittest
import numpy as np
import tempfile
import json
from pathlib import Path
import sys

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.silence_utils import (
    detect_silence_regions, segment_audio, validate_silence_detection
)

class TestSilenceDetection(unittest.TestCase):
    """Test silence detection functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sr = 22050
        self.test_config = {
            'quality_thresholds': {
                'min_music_percentage': 70.0,
                'max_silence_percentage': 30.0,
                'min_segments_per_file': 1,
                'max_segments_per_file': 10,
                'min_segment_duration': 5.0
            }
        }
    
    def create_test_audio(self, pattern: str) -> np.ndarray:
        """Create test audio with known silence/music pattern."""
        if pattern == "music_silence_music":
            # 2s music + 1s silence + 2s music
            music1 = 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, 2, 2*self.sr))
            silence = np.zeros(int(1*self.sr))
            music2 = 0.5 * np.sin(2 * np.pi * 880 * np.linspace(0, 2, 2*self.sr))
            return np.concatenate([music1, silence, music2])
        
        elif pattern == "silence_music_silence":
            # 1s silence + 3s music + 1s silence
            silence1 = np.zeros(int(1*self.sr))
            music = 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, 3, 3*self.sr))
            silence2 = np.zeros(int(1*self.sr))
            return np.concatenate([silence1, music, silence2])
        
        elif pattern == "continuous_music":
            # 5s continuous music
            return 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, 5, 5*self.sr))
        
        elif pattern == "mostly_silence":
            # 0.5s music + 4.5s silence
            music = 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, 0.5, int(0.5*self.sr)))
            silence = np.zeros(int(4.5*self.sr))
            return np.concatenate([music, silence])
        
        else:
            raise ValueError(f"Unknown pattern: {pattern}")
    
    def test_basic_silence_detection(self):
        """Test basic silence detection functionality."""
        audio = self.create_test_audio("music_silence_music")
        
        silence_map = detect_silence_regions(
            audio, self.sr,
            silence_threshold=-70,
            adaptive_threshold=False,
            min_silence_duration=0.5,
            min_music_duration=1.0,
            use_multiband=False
        )
        
        # Should detect one silence region around 2-3 seconds
        self.assertGreaterEqual(len(silence_map["silence_regions"]), 1)
        
        # Should detect two music regions
        self.assertEqual(len(silence_map["music_regions"]), 2)
        
        # Check timing of first silence region
        silence_region = silence_map["silence_regions"][0]
        self.assertGreater(silence_region["start"], 1.5)
        self.assertLess(silence_region["start"], 2.5)
        self.assertGreater(silence_region["end"], 2.5)
        self.assertLess(silence_region["end"], 3.5)
    
    def test_adaptive_thresholding(self):
        """Test adaptive thresholding functionality."""
        audio = self.create_test_audio("music_silence_music")
        
        # Test with adaptive thresholding enabled
        silence_map_adaptive = detect_silence_regions(
            audio, self.sr,
            adaptive_threshold=True,
            percentile_threshold=20,
            silence_threshold=-70,
            use_multiband=False
        )

        # Test with fixed thresholding
        silence_map_fixed = detect_silence_regions(
            audio, self.sr,
            adaptive_threshold=False,
            silence_threshold=-70,
            use_multiband=False
        )
        
        # Both should detect silence, but adaptive might be more accurate
        self.assertGreater(len(silence_map_adaptive["silence_regions"]), 0)
        self.assertGreater(len(silence_map_fixed["silence_regions"]), 0)
        
        # Adaptive threshold should be stored in params
        self.assertIn("silence_threshold", silence_map_adaptive["detection_params"])
    
    def test_multiband_analysis(self):
        """Test multi-band analysis functionality."""
        audio = self.create_test_audio("music_silence_music")
        
        # Test with multi-band enabled
        silence_map_multiband = detect_silence_regions(
            audio, self.sr,
            use_multiband=True,
            silence_threshold=-70,
            adaptive_threshold=False
        )

        # Test with single-band
        silence_map_singleband = detect_silence_regions(
            audio, self.sr,
            use_multiband=False,
            silence_threshold=-70,
            adaptive_threshold=False
        )
        
        # Both should work, multi-band might be more accurate
        self.assertGreater(len(silence_map_multiband["silence_regions"]), 0)
        self.assertGreater(len(silence_map_singleband["silence_regions"]), 0)
        
        # Check that multiband flag is stored
        self.assertTrue(silence_map_multiband["detection_params"]["use_multiband"])
        self.assertFalse(silence_map_singleband["detection_params"]["use_multiband"])
    
    def test_morphological_smoothing(self):
        """Test morphological smoothing functionality."""
        audio = self.create_test_audio("music_silence_music")
        
        # Test with smoothing enabled
        silence_map_smooth = detect_silence_regions(
            audio, self.sr,
            apply_smoothing=True,
            silence_threshold=-70,
            adaptive_threshold=False,
            use_multiband=False
        )

        # Test without smoothing
        silence_map_raw = detect_silence_regions(
            audio, self.sr,
            apply_smoothing=False,
            silence_threshold=-70,
            adaptive_threshold=False,
            use_multiband=False
        )
        
        # Both should detect silence
        self.assertGreater(len(silence_map_smooth["silence_regions"]), 0)
        self.assertGreater(len(silence_map_raw["silence_regions"]), 0)
        
        # Smoothed version might have fewer, cleaner regions
        # (This is not guaranteed, depends on the specific audio)
    
    def test_continuous_music(self):
        """Test detection on continuous music (should have minimal silence)."""
        audio = self.create_test_audio("continuous_music")

        silence_map = detect_silence_regions(
            audio, self.sr,
            silence_threshold=-70,
            adaptive_threshold=False,
            use_multiband=False
        )

        # Should have high music percentage
        self.assertGreater(silence_map["music_percentage"], 80)

        # Should have low silence percentage
        self.assertLess(silence_map["silence_percentage"], 20)

        # Should have at least one music region
        self.assertGreaterEqual(len(silence_map["music_regions"]), 1)
    
    def test_mostly_silence(self):
        """Test detection on mostly silent audio."""
        audio = self.create_test_audio("mostly_silence")

        silence_map = detect_silence_regions(
            audio, self.sr,
            silence_threshold=-70,
            adaptive_threshold=False,
            use_multiband=False,
            min_silence_duration=0.3
        )

        # Should have high silence percentage
        self.assertGreater(silence_map["silence_percentage"], 70)

        # Should have low music percentage
        self.assertLess(silence_map["music_percentage"], 30)


class TestAudioSegmentation(unittest.TestCase):
    """Test audio segmentation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sr = 22050
    
    def create_test_silence_map(self) -> dict:
        """Create test silence map."""
        return {
            "silence_regions": [
                {"start": 0.0, "end": 0.5, "duration": 0.5, "confidence": 0.9},
                {"start": 2.5, "end": 3.0, "duration": 0.5, "confidence": 0.8}
            ],
            "music_regions": [
                {"start": 0.5, "end": 2.5, "duration": 2.0, "energy": 0.15},
                {"start": 3.0, "end": 5.0, "duration": 2.0, "energy": 0.12}
            ],
            "silence_percentage": 20.0,
            "music_percentage": 80.0,
            "segment_count": 2
        }
    
    def test_basic_segmentation(self):
        """Test basic audio segmentation."""
        # Create 5-second test audio
        audio = np.random.randn(5 * self.sr) * 0.1
        silence_map = self.create_test_silence_map()
        
        segments = segment_audio(audio, self.sr, silence_map, padding=0.1, merge_short_gaps=False)
        
        # Should create 2 segments
        self.assertEqual(len(segments), 2)
        
        # Check segment properties
        for i, segment in enumerate(segments):
            self.assertEqual(segment["segment_id"], i)
            self.assertIn("audio_data", segment)
            self.assertIn("energy_profile", segment)
            self.assertTrue(segment["is_musical"])
            self.assertGreater(segment["duration"], 0)
    
    def test_segment_padding(self):
        """Test segment padding functionality."""
        audio = np.random.randn(5 * self.sr) * 0.1
        silence_map = self.create_test_silence_map()
        
        # Test with different padding values
        segments_no_pad = segment_audio(audio, self.sr, silence_map, padding=0.0)
        segments_with_pad = segment_audio(audio, self.sr, silence_map, padding=0.2)
        
        # Padded segments should be longer
        for i in range(len(segments_no_pad)):
            self.assertGreater(
                segments_with_pad[i]["duration"],
                segments_no_pad[i]["duration"]
            )
    
    def test_segment_merging(self):
        """Test segment merging for short gaps."""
        audio = np.random.randn(10 * self.sr) * 0.1
        
        # Create silence map with short gap
        silence_map = {
            "music_regions": [
                {"start": 1.0, "end": 3.0, "duration": 2.0, "energy": 0.15},
                {"start": 3.5, "end": 5.5, "duration": 2.0, "energy": 0.12}  # 0.5s gap
            ]
        }
        
        # Test without merging
        segments_no_merge = segment_audio(
            audio, self.sr, silence_map, 
            merge_short_gaps=False
        )
        
        # Test with merging
        segments_with_merge = segment_audio(
            audio, self.sr, silence_map, 
            merge_short_gaps=True, 
            max_gap_duration=1.0
        )
        
        # Should merge into fewer segments
        self.assertLessEqual(len(segments_with_merge), len(segments_no_merge))
    
    def test_long_segment_splitting(self):
        """Test splitting of very long segments."""
        audio = np.random.randn(400 * self.sr) * 0.1  # 400 seconds
        
        # Create silence map with one very long music region
        silence_map = {
            "music_regions": [
                {"start": 0.0, "end": 400.0, "duration": 400.0, "energy": 0.15}
            ]
        }
        
        segments = segment_audio(
            audio, self.sr, silence_map, 
            max_segment_duration=300.0
        )
        
        # Should split into multiple segments
        self.assertGreater(len(segments), 1)
        
        # Each segment should be <= max duration (plus padding)
        for segment in segments:
            self.assertLessEqual(segment["duration"], 310.0)  # 300 + padding tolerance


class TestValidation(unittest.TestCase):
    """Test validation functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'quality_thresholds': {
                'min_music_percentage': 70.0,
                'max_silence_percentage': 30.0,
                'min_segments_per_file': 1,
                'max_segments_per_file': 10,
                'min_segment_duration': 5.0
            }
        }
    
    def test_valid_silence_map(self):
        """Test validation of valid silence map."""
        silence_map = {
            "music_percentage": 85.0,
            "silence_percentage": 15.0,
            "segment_count": 3,
            "music_regions": [
                {"duration": 2.0},
                {"duration": 3.0},
                {"duration": 4.0}
            ]
        }
        
        is_valid, issues = validate_silence_detection(silence_map, self.config)
        
        self.assertTrue(is_valid)
        self.assertEqual(len(issues), 0)
    
    def test_invalid_music_percentage(self):
        """Test validation failure for low music percentage."""
        silence_map = {
            "music_percentage": 50.0,  # Below 70% threshold
            "silence_percentage": 50.0,
            "segment_count": 2,
            "music_regions": [{"duration": 3.0}, {"duration": 2.0}]
        }
        
        is_valid, issues = validate_silence_detection(silence_map, self.config)
        
        self.assertFalse(is_valid)
        self.assertGreater(len(issues), 0)
        self.assertTrue(any("Music percentage" in issue for issue in issues))
    
    def test_invalid_segment_count(self):
        """Test validation failure for invalid segment count."""
        silence_map = {
            "music_percentage": 85.0,
            "silence_percentage": 15.0,
            "segment_count": 15,  # Above 10 threshold
            "music_regions": [{"duration": 1.0}] * 15
        }
        
        is_valid, issues = validate_silence_detection(silence_map, self.config)
        
        self.assertFalse(is_valid)
        self.assertTrue(any("Segment count" in issue for issue in issues))
    
    def test_insufficient_music_duration(self):
        """Test validation failure for insufficient total music duration."""
        silence_map = {
            "music_percentage": 85.0,
            "silence_percentage": 15.0,
            "segment_count": 2,
            "music_regions": [
                {"duration": 1.0},
                {"duration": 2.0}  # Total 3.0s < 5.0s threshold
            ]
        }
        
        is_valid, issues = validate_silence_detection(silence_map, self.config)
        
        self.assertFalse(is_valid)
        self.assertTrue(any("Total music duration" in issue for issue in issues))


if __name__ == '__main__':
    unittest.main()
