"""
Test script for Phase 4: Beat Position Estimation
Validates beat detection functionality with sample data.
"""

import numpy as np
import librosa
import json
import tempfile
from pathlib import Path
import matplotlib.pyplot as plt
import sys
import os

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from phases.phase_04_beat_estimation import Phase4BeatEstimator


def create_synthetic_audio(duration=8.0, bpm=120, sr=22050):
    """Create synthetic audio with clear beats for testing."""
    
    # Calculate beat interval
    beat_interval = 60.0 / bpm
    t = np.linspace(0, duration, int(sr * duration))
    
    # Create base audio
    audio = np.zeros_like(t)
    
    # Add beats every beat_interval seconds
    for beat_time in np.arange(0, duration, beat_interval):
        beat_sample = int(beat_time * sr)
        if beat_sample < len(audio):
            # Add short percussive hit (kick drum-like)
            hit_duration = int(0.05 * sr)  # 50ms
            end_sample = min(beat_sample + hit_duration, len(audio))
            
            # Create kick drum envelope
            envelope = np.exp(-np.linspace(0, 5, end_sample - beat_sample))
            
            # Add low frequency component (kick)
            kick_freq = 60  # Hz
            kick = 0.8 * envelope * np.sin(2 * np.pi * kick_freq * t[beat_sample:end_sample])
            
            # Add high frequency component (click)
            click_freq = 2000  # Hz
            click = 0.3 * envelope * np.sin(2 * np.pi * click_freq * t[beat_sample:end_sample])
            
            audio[beat_sample:end_sample] += kick + click
    
    # Add some background music (simple chord progression)
    chord_freq = 220  # A3
    background = 0.1 * np.sin(2 * np.pi * chord_freq * t)
    background += 0.05 * np.sin(2 * np.pi * chord_freq * 1.25 * t)  # Major third
    background += 0.05 * np.sin(2 * np.pi * chord_freq * 1.5 * t)   # Perfect fifth
    
    audio += background
    
    # Normalize
    audio = audio / np.max(np.abs(audio)) * 0.8
    
    return audio


def test_beat_detection():
    """Test beat detection with synthetic audio."""
    
    print("Testing Phase 4 Beat Detection...")
    
    # Create test audio
    test_bpm = 128
    test_duration = 10.0
    sr = 22050
    
    print(f"Creating synthetic audio: {test_bpm} BPM, {test_duration}s duration")
    audio = create_synthetic_audio(duration=test_duration, bpm=test_bpm, sr=sr)
    
    # Initialize estimator
    estimator = Phase4BeatEstimator()
    
    # Test beat detection
    print("Running beat detection...")
    beat_results = estimator.detect_beats_multi_method(
        audio, sr=sr, expected_bpm=test_bpm, method="librosa"
    )
    
    # Test onset detection
    print("Running onset detection...")
    onset_results = estimator.detect_onsets(audio, sr=sr)
    
    # Analyze results
    print("\n" + "="*50)
    print("BEAT DETECTION RESULTS")
    print("="*50)
    print(f"Expected BPM: {test_bpm}")
    print(f"Detected tempo: {beat_results['tempo']:.1f} BPM")
    print(f"Tempo confidence: {beat_results['tempo_confidence']:.3f}")
    print(f"Number of beats detected: {len(beat_results['beats'])}")
    print(f"Detection method: {beat_results['detection_method']}")
    print(f"Segment duration: {beat_results['segment_duration']:.1f}s")
    
    # Calculate expected vs actual beats
    expected_beats = int(test_duration * test_bpm / 60)
    actual_beats = len(beat_results['beats'])
    beat_accuracy = min(actual_beats, expected_beats) / max(actual_beats, expected_beats)
    
    print(f"Expected beats: {expected_beats}")
    print(f"Beat detection accuracy: {beat_accuracy:.3f}")
    
    print("\n" + "="*50)
    print("ONSET DETECTION RESULTS")
    print("="*50)
    print(f"Number of onsets detected: {len(onset_results['onsets'])}")
    print(f"Onset density: {onset_results['onset_density']:.2f} onsets/sec")
    
    # Show first few beats and onsets
    if beat_results['beats']:
        print("\nFirst 5 detected beats:")
        for i, beat in enumerate(beat_results['beats'][:5]):
            print(f"  Beat {i+1}: {beat['time']:.3f}s, strength: {beat['strength']:.3f}")
    
    if onset_results['onsets']:
        print("\nFirst 5 detected onsets:")
        for i, onset in enumerate(onset_results['onsets'][:5]):
            print(f"  Onset {i+1}: {onset['time']:.3f}s, strength: {onset['strength']:.3f}, type: {onset['onset_type']}")
    
    # Test validation
    validation = estimator._validate_segment_results(beat_results, onset_results)
    print(f"\nValidation passed: {validation['passed']}")
    if validation['warnings']:
        print("Warnings:", validation['warnings'])
    if validation['errors']:
        print("Errors:", validation['errors'])
    
    # Create visualization
    print("\nCreating test visualization...")
    with tempfile.TemporaryDirectory() as temp_dir:
        viz_path = Path(temp_dir) / "test_beat_detection.png"
        estimator.visualize_beat_detection(audio, sr, beat_results, onset_results, viz_path)
        
        if viz_path.exists():
            print(f"Visualization saved to: {viz_path}")
            # Copy to a permanent location for inspection
            perm_viz_path = Path("test_beat_detection_output.png")
            import shutil
            shutil.copy(viz_path, perm_viz_path)
            print(f"Visualization copied to: {perm_viz_path}")
    
    return beat_results, onset_results


def test_real_segment():
    """Test with a real audio segment from Phase 3."""
    
    print("\n" + "="*60)
    print("Testing with real audio segment...")
    
    # Find a real segment file
    phase3_dir = Path("data/processed/phase3/audio_segments")
    if not phase3_dir.exists():
        print("Phase 3 data not found, skipping real segment test")
        return None
    
    segment_files = list(phase3_dir.glob("*_segment_*.npy"))
    if not segment_files:
        print("No segment files found, skipping real segment test")
        return None
    
    # Use the first available segment
    test_file = segment_files[0]
    print(f"Testing with: {test_file.name}")
    
    # Initialize estimator
    estimator = Phase4BeatEstimator()
    
    # Process the segment
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_output = Path(temp_dir)
        result = estimator.process_single_segment(test_file, temp_output)
        
        print(f"Processing result: {result['success']}")
        if result['success']:
            print(f"Beats detected: {result['beat_count']}")
            print(f"Onsets detected: {result['onset_count']}")
            print(f"Tempo: {result['tempo']:.1f} BPM")
            print(f"Tempo confidence: {result['tempo_confidence']:.3f}")
            print(f"Processing time: {result['processing_time']:.1f}ms")
            print(f"Quality score: {result['quality_score']:.3f}")
            print(f"Validation passed: {result['validation_passed']}")
        else:
            print(f"Error: {result['error']}")
    
    return result


def main():
    """Run all tests."""
    
    print("="*60)
    print("PHASE 4 BEAT ESTIMATION - TEST SUITE")
    print("="*60)
    
    try:
        # Test 1: Synthetic audio
        beat_results, onset_results = test_beat_detection()
        
        # Test 2: Real segment (if available)
        real_result = test_real_segment()
        
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        # Synthetic test results
        tempo_error = abs(beat_results['tempo'] - 128) / 128
        print(f"Synthetic audio test:")
        print(f"  - Tempo accuracy: {(1-tempo_error)*100:.1f}%")
        print(f"  - Beats detected: {len(beat_results['beats'])}")
        print(f"  - Onsets detected: {len(onset_results['onsets'])}")
        print(f"  - Quality score: {beat_results['processing_metadata']['quality_score']:.3f}")
        
        # Real segment test results
        if real_result and real_result['success']:
            print(f"Real segment test:")
            print(f"  - Processing successful: ✓")
            print(f"  - Beats detected: {real_result['beat_count']}")
            print(f"  - Tempo: {real_result['tempo']:.1f} BPM")
            print(f"  - Quality score: {real_result['quality_score']:.3f}")
        
        print("\nPhase 4 testing completed successfully!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
