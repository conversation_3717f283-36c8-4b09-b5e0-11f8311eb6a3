"""
Visualization utilities for Phase 5: Tempo Alignment & BPM Validation

This module provides functions to create visualizations of tempo alignment
results, BPM validation, and timing analysis.
"""

import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Optional
import seaborn as sns

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")


def plot_tempo_alignment(alignment_result: Dict, song_name: str, 
                        output_path: Optional[Path] = None) -> Path:
    """
    Create a visualization of tempo alignment results.
    
    Args:
        alignment_result: Results from tempo alignment
        song_name: Name of the song
        output_path: Path to save the plot
        
    Returns:
        Path to the saved plot
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle(f'Tempo Alignment Analysis: {song_name}', fontsize=16, fontweight='bold')
    
    beat_grid = alignment_result.get("beat_grid", [])
    if not beat_grid:
        # Create empty plot
        for ax in axes.flat:
            ax.text(0.5, 0.5, 'No beat data available', 
                   ha='center', va='center', transform=ax.transAxes)
        if output_path:
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
        return output_path or Path("tempo_alignment.png")
    
    # Extract data
    beat_times = [beat["beat_time"] for beat in beat_grid]
    original_times = [beat["original_time"] for beat in beat_grid]
    corrections = [beat["correction"] for beat in beat_grid]
    confidences = [beat["confidence"] for beat in beat_grid]
    
    # Plot 1: Beat alignment comparison
    ax1 = axes[0, 0]
    ax1.scatter(original_times, range(len(original_times)), 
               alpha=0.7, label='Original beats', s=30)
    ax1.scatter(beat_times, range(len(beat_times)), 
               alpha=0.7, label='Aligned beats', s=30)
    ax1.set_xlabel('Time (seconds)')
    ax1.set_ylabel('Beat number')
    ax1.set_title('Beat Alignment Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Timing corrections
    ax2 = axes[0, 1]
    correction_ms = [c * 1000 for c in corrections]  # Convert to milliseconds
    ax2.plot(correction_ms, 'o-', alpha=0.7, markersize=4)
    ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax2.set_xlabel('Beat number')
    ax2.set_ylabel('Correction (ms)')
    ax2.set_title('Timing Corrections Applied')
    ax2.grid(True, alpha=0.3)
    
    # Add statistics
    mean_correction = np.mean(np.abs(correction_ms))
    max_correction = np.max(np.abs(correction_ms))
    ax2.text(0.02, 0.98, f'Mean: {mean_correction:.1f}ms\nMax: {max_correction:.1f}ms',
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # Plot 3: Beat confidence distribution
    ax3 = axes[1, 0]
    ax3.hist(confidences, bins=20, alpha=0.7, edgecolor='black')
    ax3.axvline(x=np.mean(confidences), color='red', linestyle='--', 
               label=f'Mean: {np.mean(confidences):.3f}')
    ax3.set_xlabel('Confidence')
    ax3.set_ylabel('Frequency')
    ax3.set_title('Beat Confidence Distribution')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Plot 4: Tempo stability analysis
    ax4 = axes[1, 1]
    if len(beat_times) > 1:
        intervals = np.diff(beat_times)
        local_bpms = 60.0 / intervals
        ax4.plot(local_bpms, 'o-', alpha=0.7, markersize=4)
        ax4.axhline(y=alignment_result["aligned_bpm"], color='red', linestyle='--',
                   label=f'Target BPM: {alignment_result["aligned_bpm"]:.1f}')
        ax4.set_xlabel('Beat interval')
        ax4.set_ylabel('Local BPM')
        ax4.set_title('Tempo Stability')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        # Add tempo drift info
        tempo_drift = alignment_result.get("tempo_drift", 0)
        ax4.text(0.02, 0.98, f'Tempo drift: {tempo_drift:.2f}%',
                transform=ax4.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    else:
        ax4.text(0.5, 0.5, 'Insufficient data for tempo analysis',
                ha='center', va='center', transform=ax4.transAxes)
    
    plt.tight_layout()
    
    if output_path is None:
        output_path = Path(f"tempo_alignment_{song_name}.png")
    
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    return output_path


def plot_bpm_validation_summary(validation_results: List[Dict], 
                               output_path: Optional[Path] = None) -> Path:
    """
    Create a summary visualization of BPM validation results.
    
    Args:
        validation_results: List of validation results for all songs
        output_path: Path to save the plot
        
    Returns:
        Path to the saved plot
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('BPM Validation Summary', fontsize=16, fontweight='bold')
    
    if not validation_results:
        for ax in axes.flat:
            ax.text(0.5, 0.5, 'No validation data available',
                   ha='center', va='center', transform=ax.transAxes)
        if output_path:
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
        return output_path or Path("bpm_validation_summary.png")
    
    # Extract data
    bpm_errors = [result.get("bpm_error_percentage", 0) for result in validation_results]
    validation_passed = [result.get("validation_passed", False) for result in validation_results]
    tja_bpms = [result.get("tja_bpm", 0) for result in validation_results]
    detected_bpms = [result.get("detected_bpm", 0) for result in validation_results]
    
    # Plot 1: BPM error distribution
    ax1 = axes[0, 0]
    ax1.hist(bpm_errors, bins=30, alpha=0.7, edgecolor='black')
    ax1.axvline(x=np.mean(bpm_errors), color='red', linestyle='--',
               label=f'Mean: {np.mean(bpm_errors):.2f}%')
    ax1.axvline(x=5.0, color='orange', linestyle='--',
               label='5% threshold')
    ax1.set_xlabel('BPM Error (%)')
    ax1.set_ylabel('Frequency')
    ax1.set_title('BPM Error Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Validation pass rate
    ax2 = axes[0, 1]
    pass_count = sum(validation_passed)
    fail_count = len(validation_passed) - pass_count
    
    labels = ['Passed', 'Failed']
    sizes = [pass_count, fail_count]
    colors = ['lightgreen', 'lightcoral']
    
    wedges, texts, autotexts = ax2.pie(sizes, labels=labels, colors=colors,
                                      autopct='%1.1f%%', startangle=90)
    ax2.set_title(f'Validation Results\n({pass_count}/{len(validation_passed)} passed)')
    
    # Plot 3: TJA vs Detected BPM scatter
    ax3 = axes[1, 0]
    colors = ['green' if passed else 'red' for passed in validation_passed]
    ax3.scatter(tja_bpms, detected_bpms, c=colors, alpha=0.6)
    
    # Add perfect alignment line
    min_bpm = min(min(tja_bpms), min(detected_bpms))
    max_bpm = max(max(tja_bpms), max(detected_bpms))
    ax3.plot([min_bpm, max_bpm], [min_bpm, max_bpm], 'k--', alpha=0.5)
    
    ax3.set_xlabel('TJA BPM')
    ax3.set_ylabel('Detected BPM')
    ax3.set_title('TJA vs Detected BPM')
    ax3.grid(True, alpha=0.3)
    
    # Add legend
    from matplotlib.lines import Line2D
    legend_elements = [Line2D([0], [0], marker='o', color='w', markerfacecolor='green',
                             markersize=8, label='Passed'),
                      Line2D([0], [0], marker='o', color='w', markerfacecolor='red',
                             markersize=8, label='Failed')]
    ax3.legend(handles=legend_elements)
    
    # Plot 4: BPM range analysis
    ax4 = axes[1, 1]
    bpm_ranges = []
    range_labels = ['<100', '100-120', '120-140', '140-160', '160-180', '>180']
    
    for bpm in tja_bpms:
        if bpm < 100:
            bpm_ranges.append(0)
        elif bpm < 120:
            bpm_ranges.append(1)
        elif bpm < 140:
            bpm_ranges.append(2)
        elif bpm < 160:
            bpm_ranges.append(3)
        elif bpm < 180:
            bpm_ranges.append(4)
        else:
            bpm_ranges.append(5)
    
    range_counts = [bpm_ranges.count(i) for i in range(6)]
    ax4.bar(range_labels, range_counts, alpha=0.7)
    ax4.set_xlabel('BPM Range')
    ax4.set_ylabel('Song Count')
    ax4.set_title('BPM Range Distribution')
    ax4.grid(True, alpha=0.3, axis='y')
    
    plt.tight_layout()
    
    if output_path is None:
        output_path = Path("bpm_validation_summary.png")
    
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    return output_path


def create_phase_05_report(results_dir: Path, output_path: Optional[Path] = None) -> Path:
    """
    Create a comprehensive report with visualizations for Phase 5 results.
    
    Args:
        results_dir: Directory containing Phase 5 results
        output_path: Path to save the report
        
    Returns:
        Path to the saved report
    """
    # Load validation results
    validation_dir = results_dir / "bpm_validation"
    validation_files = list(validation_dir.glob("*.json"))
    
    validation_results = []
    for file in validation_files:
        try:
            with open(file, 'r') as f:
                validation_results.append(json.load(f))
        except Exception as e:
            print(f"Error loading {file}: {e}")
    
    # Create summary visualization
    viz_dir = results_dir / "visualizations"
    viz_dir.mkdir(exist_ok=True)
    
    summary_plot = plot_bpm_validation_summary(
        validation_results, 
        viz_dir / "bpm_validation_summary.png"
    )
    
    # Create individual alignment plots for a few songs
    alignment_dir = results_dir / "tempo_alignment"
    alignment_files = list(alignment_dir.glob("*.json"))[:5]  # First 5 songs
    
    for file in alignment_files:
        try:
            with open(file, 'r') as f:
                alignment_result = json.load(f)
            
            song_name = file.stem
            plot_path = viz_dir / f"alignment_{song_name}.png"
            plot_tempo_alignment(alignment_result, song_name, plot_path)
            
        except Exception as e:
            print(f"Error creating plot for {file}: {e}")
    
    print(f"Phase 5 visualizations created in {viz_dir}")
    return viz_dir


if __name__ == "__main__":
    # Example usage
    results_dir = Path("data/processed/phase5")
    if results_dir.exists():
        create_phase_05_report(results_dir)
    else:
        print(f"Results directory {results_dir} not found")
