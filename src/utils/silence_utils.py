"""
Silence Detection Utilities for Phase 3
Implements multi-band silence detection and audio segmentation algorithms.
"""

import numpy as np
import librosa
import scipy.signal
from scipy.ndimage import binary_opening, binary_closing
from typing import Dict, List, Tuple, Optional
import logging

def detect_silence_regions(
    audio: np.n<PERSON><PERSON>,
    sr: int = 22050,
    frame_length: int = 2048,
    hop_length: int = 512,
    silence_threshold: float = -40,
    adaptive_threshold: bool = True,
    percentile_threshold: int = 20,
    threshold_offset: float = -10.0,
    min_silence_duration: float = 0.5,
    min_music_duration: float = 1.0,
    use_multiband: bool = True,
    band_threshold_offset: float = 5.0,
    apply_smoothing: bool = True,
    opening_kernel_ratio: float = 0.8,
    closing_kernel_ratio: float = 1.2,
    fast_mode: bool = False
) -> Dict:
    """
    Detect silence and music regions using multi-band energy analysis.
    
    Args:
        audio: Input audio signal
        sr: Sample rate
        frame_length: Frame size for analysis
        hop_length: Hop size for analysis
        silence_threshold: Base silence threshold in dB
        adaptive_threshold: Use adaptive thresholding
        percentile_threshold: Percentile for adaptive threshold
        threshold_offset: Offset from adaptive threshold
        min_silence_duration: Minimum silence duration to consider
        min_music_duration: Minimum music duration to consider
        use_multiband: Enable multi-band analysis
        band_threshold_offset: Additional offset for frequency bands
        apply_smoothing: Apply morphological smoothing
        opening_kernel_ratio: Opening kernel size ratio
        closing_kernel_ratio: Closing kernel size ratio
        
    Returns:
        Dictionary with silence and music regions
    """
    
    # 1. Calculate RMS energy (optimized)
    if fast_mode:
        # Use smaller frame size for faster processing
        frame_length = min(frame_length, 1024)
        hop_length = min(hop_length, 256)

    rms_energy = librosa.feature.rms(
        y=audio,
        frame_length=frame_length,
        hop_length=hop_length
    )[0]

    # Convert to dB with optimized reference
    rms_db = librosa.amplitude_to_db(rms_energy, ref=np.max)
    
    # 2. Multi-band analysis for better detection (optimized)
    if use_multiband and not fast_mode:
        stft = librosa.stft(audio, hop_length=hop_length, n_fft=frame_length)
        magnitude = np.abs(stft)

        # Split into frequency bands (optimized indexing)
        n_bins = magnitude.shape[0]
        band_size = n_bins // 3
        low_band = np.mean(magnitude[:band_size], axis=0)
        mid_band = np.mean(magnitude[band_size:2*band_size], axis=0)
        high_band = np.mean(magnitude[2*band_size:], axis=0)

        # Convert to dB with shared reference
        ref_val = np.max(magnitude)
        low_db = librosa.amplitude_to_db(low_band, ref=ref_val)
        mid_db = librosa.amplitude_to_db(mid_band, ref=ref_val)
        high_db = librosa.amplitude_to_db(high_band, ref=ref_val)
    elif fast_mode:
        # Skip multi-band analysis in fast mode
        use_multiband = False
    
    # 3. Adaptive threshold based on signal statistics
    if adaptive_threshold:
        # Use a more conservative approach for adaptive thresholding
        low_percentile = np.percentile(rms_db, percentile_threshold)
        high_percentile = np.percentile(rms_db, 80)  # 80th percentile

        # If there's a big gap between low and high percentiles, use adaptive
        if (high_percentile - low_percentile) > 20:  # 20dB difference suggests silence/music distinction
            adaptive_thresh = low_percentile + threshold_offset
        else:
            adaptive_thresh = silence_threshold

        final_threshold = max(silence_threshold, adaptive_thresh)
    else:
        final_threshold = silence_threshold

    # Ensure threshold is reasonable for the signal
    signal_max = np.max(rms_db)
    signal_min = np.min(rms_db[rms_db > -np.inf])  # Exclude -inf values

    # If threshold is too close to max, adjust it
    if final_threshold > signal_max - 10:
        final_threshold = signal_max - 20

    # If threshold is too close to min, adjust it
    if final_threshold < signal_min + 5:
        final_threshold = signal_min + 10

    # Special case: if signal has very little variation (continuous music),
    # use a more conservative threshold
    signal_range = signal_max - signal_min
    if signal_range < 10:  # Less than 10dB variation
        final_threshold = signal_max - 15  # 15dB below max
    
    # 4. Combine multi-band information
    if use_multiband:
        # A frame is silent if ALL bands are below threshold
        silence_mask = (
            (rms_db < final_threshold) &
            (low_db < final_threshold + band_threshold_offset) &
            (mid_db < final_threshold + band_threshold_offset) &
            (high_db < final_threshold + band_threshold_offset)
        )
    else:
        silence_mask = rms_db < final_threshold

    # Additional check: frames with very low absolute energy are likely silence
    if use_multiband:
        # Use minimum energy across all bands
        min_energy_db = np.minimum(np.minimum(low_db, mid_db), high_db)
        very_quiet_mask = min_energy_db < (final_threshold - 10)
        silence_mask = silence_mask | very_quiet_mask

    # Direct check for true silence (very low RMS energy)
    # Only apply this if we're not already detecting most frames as silence
    if np.mean(silence_mask) < 0.8:  # If less than 80% is already detected as silence
        true_silence_mask = rms_db <= -70  # Very low energy threshold
        silence_mask = silence_mask | true_silence_mask
    
    # 5. Apply morphological operations to clean up detection
    if apply_smoothing:
        # Calculate kernel sizes based on minimum durations (but keep them reasonable)
        opening_kernel_size = max(1, min(5, int(min_silence_duration * sr / hop_length * opening_kernel_ratio)))
        closing_kernel_size = max(1, min(10, int(min_silence_duration * sr / hop_length * closing_kernel_ratio)))

        # Smooth the mask
        silence_mask = binary_opening(silence_mask, structure=np.ones(opening_kernel_size))
        silence_mask = binary_closing(silence_mask, structure=np.ones(closing_kernel_size))
    
    # 6. Convert frame indices to time regions
    frame_times = librosa.frames_to_time(
        np.arange(len(silence_mask)), 
        sr=sr, 
        hop_length=hop_length
    )
    
    silence_regions = []
    music_regions = []
    
    # Find contiguous silence regions
    # Add padding to handle edge cases
    padded_mask = np.concatenate(([False], silence_mask, [False]))
    mask_diff = np.diff(padded_mask.astype(int))

    silence_starts = np.where(mask_diff == 1)[0]  # Transitions from False to True
    silence_ends = np.where(mask_diff == -1)[0]   # Transitions from True to False

    for start_idx, end_idx in zip(silence_starts, silence_ends):
        start_time = frame_times[start_idx] if start_idx < len(frame_times) else frame_times[-1]
        end_time = frame_times[min(end_idx - 1, len(frame_times) - 1)]  # end_idx is exclusive
        duration = end_time - start_time
        
        if duration >= min_silence_duration:
            # Calculate confidence based on how far below threshold
            region_energy = rms_db[start_idx:end_idx+1]
            confidence = max(0, (final_threshold - np.mean(region_energy)) / 10)
            
            silence_regions.append({
                "start": float(start_time),
                "end": float(end_time),
                "duration": float(duration),
                "confidence": float(min(1.0, confidence))
            })
    
    # Find music regions (inverse of silence)
    music_mask = ~silence_mask
    padded_music_mask = np.concatenate(([False], music_mask, [False]))
    music_mask_diff = np.diff(padded_music_mask.astype(int))

    music_starts = np.where(music_mask_diff == 1)[0]  # Transitions from False to True
    music_ends = np.where(music_mask_diff == -1)[0]   # Transitions from True to False

    for start_idx, end_idx in zip(music_starts, music_ends):
        start_time = frame_times[start_idx] if start_idx < len(frame_times) else frame_times[-1]
        end_time = frame_times[min(end_idx - 1, len(frame_times) - 1)]  # end_idx is exclusive
        duration = end_time - start_time
        
        if duration >= min_music_duration:
            # Calculate average energy for this region
            region_energy = rms_energy[start_idx:end_idx+1]
            avg_energy = float(np.mean(region_energy))
            
            music_regions.append({
                "start": float(start_time),
                "end": float(end_time),
                "duration": float(duration),
                "energy": avg_energy
            })
    
    # 7. Calculate statistics
    total_duration = len(audio) / sr
    silence_duration = sum(r["duration"] for r in silence_regions)
    music_duration = sum(r["duration"] for r in music_regions)
    
    return {
        "silence_regions": silence_regions,
        "music_regions": music_regions,
        "silence_percentage": float(silence_duration / total_duration * 100),
        "music_percentage": float(music_duration / total_duration * 100),
        "segment_count": len(music_regions),
        "detection_params": {
            "silence_threshold": final_threshold,
            "adaptive_threshold": adaptive_threshold,
            "min_silence_duration": min_silence_duration,
            "min_music_duration": min_music_duration,
            "frame_length": frame_length,
            "hop_length": hop_length,
            "use_multiband": use_multiband
        }
    }


def segment_audio(
    audio: np.ndarray,
    sr: int,
    silence_map: Dict,
    padding: float = 0.1,
    max_segment_duration: float = 300.0,
    merge_short_gaps: bool = True,
    max_gap_duration: float = 2.0,
    energy_profile_hop: int = 512
) -> List[Dict]:
    """
    Segment audio based on silence detection results.
    
    Args:
        audio: Input audio signal
        sr: Sample rate
        silence_map: Results from detect_silence_regions
        padding: Padding around segments in seconds
        max_segment_duration: Maximum segment duration
        merge_short_gaps: Merge segments with short gaps
        max_gap_duration: Maximum gap to merge
        energy_profile_hop: Hop length for energy profiles
        
    Returns:
        List of audio segments with metadata
    """
    segments = []
    music_regions = silence_map["music_regions"].copy()
    
    # Merge segments with short gaps if enabled
    if merge_short_gaps and len(music_regions) > 1:
        merged_regions = []
        current_region = music_regions[0].copy()
        
        for next_region in music_regions[1:]:
            gap_duration = next_region["start"] - current_region["end"]
            
            if gap_duration <= max_gap_duration:
                # Merge regions
                current_region["end"] = next_region["end"]
                current_region["duration"] = current_region["end"] - current_region["start"]
                current_region["energy"] = (current_region["energy"] + next_region["energy"]) / 2
            else:
                merged_regions.append(current_region)
                current_region = next_region.copy()
        
        merged_regions.append(current_region)
        music_regions = merged_regions
    
    # Create segments
    for i, music_region in enumerate(music_regions):
        # Add padding
        start_time = max(0, music_region["start"] - padding)
        end_time = min(len(audio) / sr, music_region["end"] + padding)
        
        # Split long segments if necessary
        segment_duration = end_time - start_time
        if segment_duration > max_segment_duration:
            # Split into multiple segments
            num_splits = int(np.ceil(segment_duration / max_segment_duration))
            split_duration = segment_duration / num_splits
            
            for split_idx in range(num_splits):
                split_start = start_time + split_idx * split_duration
                split_end = min(end_time, start_time + (split_idx + 1) * split_duration)
                
                segment_info = _create_segment_info(
                    audio, sr, split_start, split_end, 
                    f"{i}_{split_idx}", music_region, padding, energy_profile_hop
                )
                segments.append(segment_info)
        else:
            segment_info = _create_segment_info(
                audio, sr, start_time, end_time, 
                i, music_region, padding, energy_profile_hop
            )
            segments.append(segment_info)
    
    return segments


def _create_segment_info(
    audio: np.ndarray, 
    sr: int, 
    start_time: float, 
    end_time: float, 
    segment_id, 
    music_region: Dict, 
    padding: float,
    energy_profile_hop: int
) -> Dict:
    """Create segment information dictionary."""
    # Convert to sample indices
    start_sample = int(start_time * sr)
    end_sample = int(end_time * sr)
    
    # Extract segment
    segment_audio = audio[start_sample:end_sample]
    
    # Calculate energy profile for segment
    segment_rms = librosa.feature.rms(
        y=segment_audio, 
        frame_length=2048, 
        hop_length=energy_profile_hop
    )[0]
    
    return {
        "segment_id": segment_id,
        "start_time": float(start_time),
        "end_time": float(end_time),
        "duration": float(end_time - start_time),
        "audio_data": segment_audio,
        "energy_profile": segment_rms,
        "is_musical": True,
        "original_start": music_region["start"],
        "original_end": music_region["end"],
        "padding_applied": padding,
        "sample_start": start_sample,
        "sample_end": end_sample
    }


def validate_silence_detection(silence_map: Dict, config: Dict) -> Tuple[bool, List[str]]:
    """
    Validate silence detection results against adaptive quality thresholds.

    Args:
        silence_map: Silence detection results
        config: Configuration dictionary with quality thresholds

    Returns:
        Tuple of (is_valid, list_of_issues)
    """
    issues = []
    thresholds = config.get('quality_thresholds', {})

    # Check music percentage with adaptive threshold
    min_music_pct = thresholds.get('min_music_percentage', 70.0)
    music_pct = silence_map['music_percentage']

    # Relax threshold for files with many segments (complex songs)
    segment_count = silence_map['segment_count']
    complex_threshold = thresholds.get('complex_song_threshold', 8)
    complex_music_pct = thresholds.get('complex_music_percentage', 60.0)

    if segment_count > complex_threshold:
        min_music_pct = complex_music_pct  # Use configured complex song threshold

    if music_pct < min_music_pct:
        issues.append(f"Music percentage {music_pct:.1f}% below minimum {min_music_pct}%")

    # Check silence percentage with adaptive threshold
    max_silence_pct = thresholds.get('max_silence_percentage', 30.0)
    complex_silence_pct = thresholds.get('complex_silence_percentage', 40.0)

    if segment_count > complex_threshold:
        max_silence_pct = complex_silence_pct  # Use configured complex song threshold

    if silence_map['silence_percentage'] > max_silence_pct:
        issues.append(f"Silence percentage {silence_map['silence_percentage']:.1f}% above maximum {max_silence_pct}%")

    # Adaptive segment count limits based on song duration
    min_segments = thresholds.get('min_segments_per_file', 1)
    max_segments = thresholds.get('max_segments_per_file', 10)

    # Calculate total song duration and apply adaptive limits
    total_duration = sum(r['duration'] for r in silence_map['music_regions'])
    long_song_duration = thresholds.get('long_song_duration', 240.0)
    long_song_max_segments = thresholds.get('long_song_max_segments', 20)

    if total_duration > long_song_duration:  # Long songs
        max_segments = long_song_max_segments  # Use configured long song limit

    if segment_count < min_segments:
        issues.append(f"Segment count {segment_count} below minimum {min_segments}")
    elif segment_count > max_segments:
        issues.append(f"Segment count {segment_count} above maximum {max_segments}")

    # Check minimum total music duration
    min_duration = thresholds.get('min_segment_duration', 5.0)
    if total_duration < min_duration:
        issues.append(f"Total music duration {total_duration:.1f}s below minimum {min_duration}s")

    return len(issues) == 0, issues
