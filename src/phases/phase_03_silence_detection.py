"""
Phase 3: Silence Detection & Audio Segmentation
Main implementation for detecting silence regions and segmenting audio into musical sections.
"""

import os
import sys
import json
import time
import logging
import gc
from pathlib import Path
from typing import Dict, List, Optional, Tuple
# Removed unused imports: ThreadPoolExecutor, as_completed
from tqdm import tqdm
import yaml
import numpy as np

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.path_utils import ensure_directory_exists
from utils.audio_utils import MemoryManager
from utils.silence_utils import (
    detect_silence_regions, segment_audio, validate_silence_detection
)

class Phase3SilenceProcessor:
    """Main processor for Phase 3: Silence Detection & Audio Segmentation."""
    
    def __init__(self, config_path: str = "configs/phase_03_config.yaml"):
        """Initialize processor with configuration."""
        self.config = self._load_config(config_path)
        self.memory_manager = MemoryManager(self.config['memory']['max_ram_usage_gb'])

        # Setup temporary console logging for initialization
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)
            self.logger.setLevel(logging.INFO)
        
        # Statistics tracking
        self.stats = {
            "total_files": 0,
            "processed_files": 0,
            "successful_files": 0,
            "failed_files": 0,
            "quality_passed_files": 0,
            "quality_failed_files": 0,
            "total_segments": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0,
            "average_silence_percentage": 0.0,
            "average_segments_per_file": 0.0,
            "errors": []
        }
        
        # Validate inputs and create output directories
        self._validate_inputs()
        self._create_output_directories()

        # Setup file logging after directories are created
        self.setup_logging()
    
    def _load_config(self, config_path: str) -> Dict:
        """Load configuration from YAML file."""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            raise RuntimeError(f"Failed to load config from {config_path}: {e}")
    
    def _create_output_directories(self):
        """Create all necessary output directories with robust error handling."""
        paths_to_create = [
            self.config['paths']['output_root'],
            self.config['paths']['silence_maps'],
            self.config['paths']['audio_segments'],
            self.config['paths']['energy_profiles'],
            self.config['paths']['logs_output']
        ]

        self.logger.info("Creating output directories...")

        for path in paths_to_create:
            try:
                # Convert to Path object and create with parents
                path_obj = Path(path)
                path_obj.mkdir(parents=True, exist_ok=True)
                self.logger.debug(f"Created directory: {path}")
            except Exception as e:
                self.logger.error(f"Failed to create directory {path}: {e}")
                raise RuntimeError(f"Cannot create output directory {path}: {e}")

        self.logger.info(f"Successfully created {len(paths_to_create)} output directories")

    def _validate_inputs(self):
        """Validate that required Phase 2 inputs exist."""
        required_paths = [
            self.config['paths']['input_audio'],
            self.config['paths']['input_metadata']
        ]

        missing_paths = []
        for path in required_paths:
            if not Path(path).exists():
                missing_paths.append(path)

        if missing_paths:
            error_msg = f"Missing required Phase 2 inputs: {missing_paths}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)

        # Check if we have audio files
        audio_files = list(Path(self.config['paths']['input_audio']).glob("*.npy"))
        if not audio_files:
            error_msg = f"No audio files found in {self.config['paths']['input_audio']}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)

        self.logger.info(f"Validated inputs: {len(audio_files)} audio files found")

    def setup_logging(self):
        """Setup file logging configuration after directories are created."""
        log_config = self.config['logging']
        log_file = Path(self.config['paths']['logs_output']) / "phase3_processing.log"

        # Clear existing handlers and setup new ones
        self.logger.handlers.clear()

        # Create file handler
        file_handler = logging.FileHandler(log_file, encoding=log_config['file_encoding'])
        file_handler.setLevel(getattr(logging, log_config['level']))

        # Create console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, log_config['level']))

        # Create formatter
        formatter = logging.Formatter(log_config['format'])
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Add handlers to logger
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        self.logger.setLevel(getattr(logging, log_config['level']))

        self.logger.info("Phase 3 Silence Detection Processor initialized with file logging")
    
    def process_single_file(self, audio_file: Path) -> Tuple[bool, Dict]:
        """
        Process a single audio file for silence detection and segmentation.
        
        Args:
            audio_file: Path to the audio file (.npy format)
            
        Returns:
            Tuple of (success, results_dict)
        """
        start_time = time.time()
        file_stem = audio_file.stem
        
        try:
            # Load audio data
            audio = np.load(audio_file)
            self.logger.debug(f"Loaded audio file: {file_stem}, shape: {audio.shape}")
            
            # Detect silence regions
            silence_map = detect_silence_regions(
                audio=audio,
                sr=22050,  # Standardized sample rate from Phase 1
                **self.config['silence_detection']
            )
            
            # Validate silence detection results
            is_valid, validation_issues = validate_silence_detection(
                silence_map, self.config
            )
            
            if not is_valid:
                self.logger.warning(f"Quality validation failed for {file_stem}: {validation_issues}")
                return False, {
                    "file": str(audio_file),
                    "error": "Quality validation failed",
                    "issues": validation_issues,
                    "processing_time": time.time() - start_time
                }
            
            # Segment audio
            segments = segment_audio(
                audio=audio,
                sr=22050,
                silence_map=silence_map,
                **self.config['segmentation']
            )
            
            # Save silence map (convert numpy types to native Python types)
            silence_map_serializable = self._convert_numpy_types(silence_map)
            silence_map_file = Path(self.config['paths']['silence_maps']) / f"{file_stem}.json"
            with open(silence_map_file, 'w', encoding='utf-8') as f:
                json.dump(silence_map_serializable, f, indent=self.config['output']['metadata_indent'])
            
            # Save segments and energy profiles
            segment_metadata = []
            for segment in segments:
                # Save segment audio
                segment_file = Path(self.config['paths']['audio_segments']) / f"{file_stem}_segment_{segment['segment_id']}.npy"
                np.save(segment_file, segment["audio_data"])
                
                # Save energy profile
                energy_file = Path(self.config['paths']['energy_profiles']) / f"{file_stem}_segment_{segment['segment_id']}_energy.npy"
                np.save(energy_file, segment["energy_profile"])
                
                # Prepare metadata (without audio_data for JSON serialization)
                segment_meta = {k: v for k, v in segment.items()
                              if k not in ["audio_data", "energy_profile"]}
                segment_meta = self._convert_numpy_types(segment_meta)
                segment_metadata.append(segment_meta)
            
            # Save segment metadata
            segments_file = Path(self.config['paths']['audio_segments']) / f"{file_stem}_segments.json"
            with open(segments_file, 'w', encoding='utf-8') as f:
                json.dump(segment_metadata, f, indent=self.config['output']['metadata_indent'])
            
            processing_time = time.time() - start_time
            
            # Clean up memory
            del audio, segments
            
            return True, {
                "file": str(audio_file),
                "silence_percentage": silence_map["silence_percentage"],
                "music_percentage": silence_map["music_percentage"],
                "segment_count": silence_map["segment_count"],
                "processing_time": processing_time,
                "quality_passed": is_valid
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"Error processing {file_stem}: {str(e)}"
            self.logger.error(error_msg)
            
            return False, {
                "file": str(audio_file),
                "error": str(e),
                "processing_time": processing_time
            }
    
    def process_batch(self, audio_files: List[Path], batch_id: int) -> List[Dict]:
        """Process a batch of audio files with optimized memory management."""
        batch_results = []

        self.logger.info(f"Processing batch {batch_id} with {len(audio_files)} files")

        # Determine if we should use fast mode for large batches
        use_fast_mode = len(audio_files) > 100 or batch_id > 200

        for i, audio_file in enumerate(tqdm(audio_files, desc=f"Batch {batch_id}", leave=False)):
            # Enable fast mode for later files in large batches
            if use_fast_mode and i > len(audio_files) // 2:
                self.config['silence_detection']['fast_mode'] = True

            success, result = self.process_single_file(audio_file)

            if success:
                self.stats["successful_files"] += 1
                if result.get("quality_passed", False):
                    self.stats["quality_passed_files"] += 1
                else:
                    self.stats["quality_failed_files"] += 1

                self.stats["total_segments"] += result.get("segment_count", 0)
            else:
                self.stats["failed_files"] += 1
                self.stats["errors"].append(result)

            self.stats["processed_files"] += 1
            batch_results.append(result)

            # Enhanced memory management
            if i % self.config['memory']['gc_frequency'] == 0:
                gc.collect()

        # Reset fast mode
        self.config['silence_detection']['fast_mode'] = False

        return batch_results
    
    def process_dataset(self, validation_mode: bool = False) -> Dict:
        """
        Process the entire dataset or validation subset.
        
        Args:
            validation_mode: If True, process only validation subset
            
        Returns:
            Processing results dictionary
        """
        start_time = time.time()
        
        # Get input files
        input_dir = Path(self.config['paths']['input_audio'])
        audio_files = list(input_dir.glob("*.npy"))
        
        if validation_mode:
            subset_size = self.config['processing']['validation_subset_size']
            audio_files = audio_files[:subset_size]
            self.logger.info(f"Running validation mode with {len(audio_files)} files")
        else:
            self.logger.info(f"Processing full dataset with {len(audio_files)} files")
        
        self.stats["total_files"] = len(audio_files)
        
        if not audio_files:
            raise RuntimeError(f"No audio files found in {input_dir}")
        
        # Process in batches
        batch_size = self.config['processing']['batch_size']
        batches = [audio_files[i:i + batch_size] for i in range(0, len(audio_files), batch_size)]
        
        all_results = []
        
        for batch_id, batch in enumerate(batches):
            batch_results = self.process_batch(batch, batch_id)
            all_results.extend(batch_results)
            
            # Memory cleanup between batches
            if batch_id % self.config['memory']['clear_cache_frequency'] == 0:
                gc.collect()
        
        # Calculate final statistics
        self.stats["total_processing_time"] = time.time() - start_time
        if self.stats["processed_files"] > 0:
            self.stats["average_processing_time"] = (
                self.stats["total_processing_time"] / self.stats["processed_files"]
            )
        
        # Calculate averages for successful files
        successful_results = [r for r in all_results if "silence_percentage" in r]
        if successful_results:
            self.stats["average_silence_percentage"] = np.mean([
                r["silence_percentage"] for r in successful_results
            ])
            self.stats["average_segments_per_file"] = np.mean([
                r["segment_count"] for r in successful_results
            ])
        
        # Save processing report
        self._save_processing_report(all_results, validation_mode)
        
        return self.stats
    
    def _save_processing_report(self, results: List[Dict], validation_mode: bool):
        """Save comprehensive processing report."""
        report = {
            "report_type": "Silence Detection & Segmentation Report",
            "validation_mode": validation_mode,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "processing_statistics": self.stats.copy(),
            "configuration": self.config,
            "quality_gates_status": self._check_quality_gates()
        }
        
        # Remove errors from stats copy to avoid duplication
        report["processing_statistics"].pop("errors", None)
        
        # Add detailed results for validation mode
        if validation_mode:
            report["detailed_results"] = results
        
        # Save report
        report_file = Path(self.config['paths']['output_root']) / "segmentation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Processing report saved to {report_file}")

    def _convert_numpy_types(self, obj):
        """Convert numpy types to native Python types for JSON serialization."""
        if isinstance(obj, dict):
            return {k: self._convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj

    def _check_quality_gates(self) -> Dict:
        """Check if processing meets quality gate requirements."""
        gates = self.config['quality_gates']
        
        success_rate = (
            self.stats["successful_files"] / max(1, self.stats["total_files"])
        )
        quality_pass_rate = (
            self.stats["quality_passed_files"] / max(1, self.stats["successful_files"])
        )
        
        return {
            "success_rate": success_rate,
            "success_rate_passed": success_rate >= gates['min_success_rate'],
            "quality_pass_rate": quality_pass_rate,
            "quality_pass_rate_passed": quality_pass_rate >= gates['min_quality_pass_rate'],
            "min_processed_files_passed": self.stats["processed_files"] >= gates['min_processed_files'],
            "overall_passed": (
                success_rate >= gates['min_success_rate'] and
                quality_pass_rate >= gates['min_quality_pass_rate'] and
                self.stats["processed_files"] >= gates['min_processed_files']
            )
        }


def main():
    """Main execution function for Phase 3 processing."""
    import argparse

    parser = argparse.ArgumentParser(description="Phase 3: Silence Detection & Audio Segmentation")
    parser.add_argument("--config", default="configs/phase_03_config.yaml",
                       help="Configuration file path")
    parser.add_argument("--validation", action="store_true",
                       help="Run validation mode with subset of files")
    parser.add_argument("--verbose", action="store_true",
                       help="Enable verbose logging")

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Initialize processor
        processor = Phase3SilenceProcessor(args.config)

        # Process dataset
        results = processor.process_dataset(validation_mode=args.validation)

        # Print summary
        print("\n" + "="*60)
        print("PHASE 3 PROCESSING SUMMARY")
        print("="*60)
        print(f"Total files: {results['total_files']}")
        print(f"Processed files: {results['processed_files']}")
        print(f"Successful files: {results['successful_files']}")
        print(f"Quality passed files: {results['quality_passed_files']}")
        print(f"Failed files: {results['failed_files']}")
        print(f"Total segments created: {results['total_segments']}")
        print(f"Average silence percentage: {results['average_silence_percentage']:.1f}%")
        print(f"Average segments per file: {results['average_segments_per_file']:.1f}")
        print(f"Total processing time: {results['total_processing_time']:.1f}s")
        print(f"Average processing time: {results['average_processing_time']:.2f}s per file")

        # Check quality gates
        gates_status = processor._check_quality_gates()
        print(f"\nQuality Gates Status: {'PASSED' if gates_status['overall_passed'] else 'FAILED'}")
        print(f"Success rate: {gates_status['success_rate']:.1%} ({'PASS' if gates_status['success_rate_passed'] else 'FAIL'})")
        print(f"Quality pass rate: {gates_status['quality_pass_rate']:.1%} ({'PASS' if gates_status['quality_pass_rate_passed'] else 'FAIL'})")

        if results['failed_files'] > 0:
            print(f"\nErrors encountered: {len(results['errors'])}")
            for error in results['errors'][:5]:  # Show first 5 errors
                print(f"  - {Path(error['file']).name}: {error.get('error', 'Unknown error')}")
            if len(results['errors']) > 5:
                print(f"  ... and {len(results['errors']) - 5} more errors")

        print("="*60)

        return 0 if gates_status['overall_passed'] else 1

    except Exception as e:
        print(f"Fatal error in Phase 3 processing: {e}")
        logging.error(f"Fatal error: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    exit(main())
