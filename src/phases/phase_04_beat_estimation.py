"""
Phase 4: Beat Position Estimation
Detects beat positions and onsets in audio segments to establish rhythmic foundation.

This phase processes audio segments from Phase 3 and generates:
- Beat positions with timing and confidence
- Onset positions for note candidate detection
- Tempo analysis and validation
- Quality metrics and processing reports

Follows specifications in docs/training_plan/phase_04_beat_estimation.md
"""

import librosa
import numpy as np
import json
import logging
import time
import gc
from pathlib import Path
from typing import Dict, List, Optional
from tqdm import tqdm
from sklearn.cluster import DBSCAN
import matplotlib.pyplot as plt
import warnings

# Suppress librosa warnings for cleaner output
warnings.filterwarnings('ignore', category=UserWarning, module='librosa')

# Configure logging
logger = logging.getLogger(__name__)


class Phase4BeatEstimator:
    """
    Phase 4 Beat Position Estimation processor.
    
    Detects beat positions and onsets in audio segments using multiple methods
    for robust rhythm analysis.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the beat estimator with configuration."""
        self.config = config or self._get_default_config()
        self.setup_logging()
        
        # Processing statistics
        self.stats = {
            "total_segments": 0,
            "processed_segments": 0,
            "failed_segments": 0,
            "avg_tempo": 0.0,
            "avg_beats_per_segment": 0.0,
            "tempo_accuracy": 0.0,
            "processing_errors": []
        }
        
        # Try to import madmom for advanced beat tracking
        self.madmom_available = self._check_madmom()
        
    def _get_default_config(self) -> Dict:
        """Get default configuration for beat estimation."""
        return {
            "sample_rate": 22050,
            "hop_length": 512,
            "detection_method": "combined",  # "librosa", "madmom", "combined"
            "use_tja_bpm": True,
            "onset_threshold": 0.3,
            "beat_confidence_threshold": 0.7,
            "tempo_confidence_threshold": 0.6,
            "max_beat_interval_variance": 0.3,
            "clustering_eps": 0.05,  # 50ms tolerance for beat clustering
            "visualization": False,  # Enable beat detection visualizations (disabled by default for performance)
            "batch_size": 100,
            "max_processing_time": 30,  # seconds per segment
            "memory_limit_mb": 2048
        }
    
    def setup_logging(self):
        """Setup logging for Phase 4."""
        log_dir = Path("data/logs")
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # Create phase-specific logger
        handler = logging.FileHandler(
            log_dir / f"phase04_{time.strftime('%Y%m%d_%H%M%S')}.log"
        )
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        
    def _check_madmom(self) -> bool:
        """Check if madmom is available for advanced beat tracking."""
        try:
            import madmom
            logger.info("Madmom available for advanced beat tracking")
            return True
        except ImportError:
            logger.warning("Madmom not available, using librosa only")
            return False
    
    def detect_beats_multi_method(
        self,
        audio: np.ndarray,
        sr: int = 22050,
        expected_bpm: Optional[float] = None,
        method: str = "combined"
    ) -> Dict:
        """
        Detect beats using multiple methods and combine results.
        
        Args:
            audio: Input audio segment
            sr: Sample rate
            expected_bpm: Expected BPM for validation (from TJA)
            method: Detection method ("librosa", "madmom", "combined")
            
        Returns:
            Dictionary with beat positions and metadata
        """
        
        results = {
            "beats": [],
            "tempo": 0.0,
            "tempo_confidence": 0.0,
            "beat_intervals": np.array([]),
            "detection_method": method,
            "segment_duration": len(audio) / sr,
            "processing_metadata": {
                "processing_time_ms": 0.0,
                "memory_usage_mb": 0.0,
                "quality_score": 0.0
            }
        }
        
        start_time = time.time()
        
        try:
            beat_times_librosa = np.array([])
            tempo_librosa = 120.0
            beat_times_madmom = np.array([])
            tempo_madmom = 120.0
            
            if method in ["librosa", "combined"]:
                # Method 1: Librosa beat tracking
                tempo_librosa, beats_librosa = librosa.beat.beat_track(
                    y=audio,
                    sr=sr,
                    hop_length=self.config["hop_length"],
                    start_bpm=expected_bpm if expected_bpm else 120,
                    tightness=100  # Higher values = more consistent tempo
                )
                
                # Convert beat frames to time
                beat_times_librosa = librosa.frames_to_time(
                    beats_librosa, sr=sr, hop_length=self.config["hop_length"]
                )
                
            if method in ["madmom", "combined"] and self.madmom_available:
                # Method 2: Madmom beat tracking (more robust)
                try:
                    import madmom
                    proc = madmom.features.beats.RNNBeatProcessor()
                    act = proc(audio)
                    beat_proc = madmom.features.beats.DBNBeatTrackingProcessor(fps=100)
                    beat_times_madmom = beat_proc(act)
                    
                    # Estimate tempo from madmom beats
                    if len(beat_times_madmom) > 1:
                        intervals = np.diff(beat_times_madmom)
                        tempo_madmom = 60.0 / np.median(intervals)
                    else:
                        tempo_madmom = 120.0
                        
                except Exception as e:
                    logger.warning(f"Madmom beat tracking failed: {e}")
                    beat_times_madmom = np.array([])
                    tempo_madmom = 120.0
            
            # Combine methods or use single method
            if method == "combined" and len(beat_times_madmom) > 0 and len(beat_times_librosa) > 0:
                # Combine beats from both methods
                all_beats = np.concatenate([beat_times_librosa, beat_times_madmom])
                
                # Cluster nearby beats to remove duplicates
                if len(all_beats) > 0:
                    clustering = DBSCAN(
                        eps=self.config["clustering_eps"], 
                        min_samples=1
                    )
                    clusters = clustering.fit_predict(all_beats.reshape(-1, 1))
                    
                    # Take median of each cluster
                    final_beats = []
                    for cluster_id in np.unique(clusters):
                        cluster_beats = all_beats[clusters == cluster_id]
                        final_beats.append(np.median(cluster_beats))
                    
                    beat_times = np.array(sorted(final_beats))
                    
                    # Use average tempo
                    tempo = (tempo_librosa + tempo_madmom) / 2
                else:
                    beat_times = beat_times_librosa
                    tempo = tempo_librosa
                    
            elif method == "madmom" and len(beat_times_madmom) > 0:
                beat_times = beat_times_madmom
                tempo = tempo_madmom
            else:
                beat_times = beat_times_librosa
                tempo = tempo_librosa
            
            # Calculate beat intervals and confidence
            if len(beat_times) > 1:
                beat_intervals = np.diff(beat_times)
                
                # Tempo confidence based on interval consistency
                interval_std = np.std(beat_intervals)
                tempo_confidence = max(0.0, 1.0 - (interval_std / np.mean(beat_intervals)))
                
                # Create beat list with metadata
                beats = []
                for i, beat_time in enumerate(beat_times):
                    # Calculate beat strength using onset strength
                    onset_strength = self._calculate_beat_strength(audio, sr, beat_time)
                    
                    # Calculate measure position (assuming 4/4 time)
                    measure_position = (i % 4) / 4.0
                    
                    beats.append({
                        "time": float(beat_time),
                        "confidence": float(tempo_confidence),
                        "strength": float(onset_strength),
                        "beat_number": int(i),
                        "measure_position": float(measure_position)
                    })
                
                results.update({
                    "beats": beats,
                    "tempo": float(tempo),
                    "tempo_confidence": float(tempo_confidence),
                    "beat_intervals": beat_intervals
                })
                
            else:
                logger.warning("No beats detected in audio segment")
                
        except Exception as e:
            logger.error(f"Beat detection failed: {e}")
            
        # Update processing metadata
        processing_time = (time.time() - start_time) * 1000
        results["processing_metadata"]["processing_time_ms"] = processing_time
        results["processing_metadata"]["quality_score"] = self._calculate_quality_score(results)
        
        return results
    
    def _calculate_beat_strength(self, audio: np.ndarray, sr: int, beat_time: float, window: float = 0.05) -> float:
        """Calculate the strength of a beat at a specific time."""
        # Convert time to sample index
        beat_sample = int(beat_time * sr)
        window_samples = int(window * sr)
        
        # Extract window around beat
        start_idx = max(0, beat_sample - window_samples)
        end_idx = min(len(audio), beat_sample + window_samples)
        
        if end_idx > start_idx:
            beat_window = audio[start_idx:end_idx]
            # Use RMS energy as beat strength
            strength = np.sqrt(np.mean(beat_window ** 2))
            return min(1.0, strength * 10)  # Normalize to 0-1 range
        else:
            return 0.0
    
    def _calculate_quality_score(self, results: Dict) -> float:
        """Calculate overall quality score for beat detection results."""
        if not results["beats"]:
            return 0.0
            
        # Factors contributing to quality score
        tempo_confidence = results.get("tempo_confidence", 0.0)
        beat_count = len(results["beats"])
        
        # Penalize very few or too many beats
        beat_count_score = 1.0
        if beat_count < 4:
            beat_count_score = beat_count / 4.0
        elif beat_count > 200:  # Too many beats might indicate noise
            beat_count_score = 200.0 / beat_count
            
        # Beat strength consistency
        strengths = [beat["strength"] for beat in results["beats"]]
        strength_consistency = 1.0 - np.std(strengths) if strengths else 0.0
        
        # Combine factors
        quality_score = (
            tempo_confidence * 0.4 +
            beat_count_score * 0.3 +
            strength_consistency * 0.3
        )
        
        return max(0.0, min(1.0, quality_score))

    def detect_onsets(
        self,
        audio: np.ndarray,
        sr: int = 22050,
        hop_length: int = 512,
        onset_threshold: float = 0.3
    ) -> Dict:
        """
        Detect onset positions using spectral analysis.

        Args:
            audio: Input audio segment
            sr: Sample rate
            hop_length: Hop length for analysis
            onset_threshold: Minimum onset strength

        Returns:
            Dictionary with onset positions and metadata
        """

        # Detect onsets using multiple methods
        onset_frames_spectral = librosa.onset.onset_detect(
            y=audio,
            sr=sr,
            hop_length=hop_length,
            units='frames',
            onset_envelope=librosa.onset.onset_strength(y=audio, sr=sr, hop_length=hop_length)
        )

        # Convert to time
        onset_times = librosa.frames_to_time(onset_frames_spectral, sr=sr, hop_length=hop_length)

        # Calculate onset strengths
        onset_envelope = librosa.onset.onset_strength(y=audio, sr=sr, hop_length=hop_length)

        onsets = []
        for i, onset_time in enumerate(onset_times):
            # Get onset strength
            frame_idx = librosa.time_to_frames(onset_time, sr=sr, hop_length=hop_length)
            if frame_idx < len(onset_envelope):
                strength = float(onset_envelope[frame_idx])

                # Classify onset type based on spectral characteristics
                onset_type = self._classify_onset_type(audio, sr, onset_time)

                # Get dominant frequency at onset
                dominant_freq = self._get_dominant_frequency(audio, sr, onset_time)

                if strength >= onset_threshold:
                    onsets.append({
                        "time": float(onset_time),
                        "strength": strength,
                        "frequency": float(dominant_freq),
                        "onset_type": onset_type
                    })

        return {
            "onsets": onsets,
            "onset_density": len(onsets) / (len(audio) / sr),
            "detection_params": {
                "hop_length": hop_length,
                "onset_threshold": onset_threshold
            }
        }

    def _classify_onset_type(self, audio: np.ndarray, sr: int, onset_time: float) -> str:
        """Classify onset as percussive, harmonic, or mixed."""
        # Extract short window around onset
        onset_sample = int(onset_time * sr)
        window_size = int(0.05 * sr)  # 50ms window
        start_idx = max(0, onset_sample - window_size // 2)
        end_idx = min(len(audio), onset_sample + window_size // 2)

        if end_idx > start_idx:
            window = audio[start_idx:end_idx]

            # Calculate spectral features
            stft = librosa.stft(window, hop_length=256)
            magnitude = np.abs(stft)

            # Spectral centroid (brightness)
            spectral_centroid = np.mean(librosa.feature.spectral_centroid(S=magnitude))

            # Spectral rolloff
            spectral_rolloff = np.mean(librosa.feature.spectral_rolloff(S=magnitude))

            # Zero crossing rate
            zcr = np.mean(librosa.feature.zero_crossing_rate(window))

            # Simple heuristic classification
            if zcr > 0.1 and spectral_centroid > 3000:
                return "percussive"
            elif spectral_rolloff < 4000:
                return "harmonic"
            else:
                return "mixed"

        return "unknown"

    def _get_dominant_frequency(self, audio: np.ndarray, sr: int, onset_time: float) -> float:
        """Get dominant frequency at onset time."""
        onset_sample = int(onset_time * sr)
        window_size = int(0.05 * sr)  # 50ms window
        start_idx = max(0, onset_sample - window_size // 2)
        end_idx = min(len(audio), onset_sample + window_size // 2)

        if end_idx > start_idx:
            window = audio[start_idx:end_idx]

            # Use FFT to find dominant frequency
            fft = np.fft.fft(window)
            freqs = np.fft.fftfreq(len(window), 1/sr)

            # Find peak frequency (positive frequencies only)
            positive_freqs = freqs[:len(freqs)//2]
            positive_fft = np.abs(fft[:len(fft)//2])

            if len(positive_fft) > 0:
                peak_idx = np.argmax(positive_fft)
                return abs(positive_freqs[peak_idx])

        return 0.0

    def load_bpm_from_tja(self, song_name: str) -> Optional[float]:
        """Load BPM from corresponding TJA file."""
        # This would load the BPM from the original TJA file
        # For now, return None as TJA parser is not implemented
        try:
            # Placeholder - implement based on TJA parser from references
            tja_path = Path("data/raw/ese") / "**" / f"{song_name}.tja"
            # Parse TJA and extract BPM
            return None  # Placeholder
        except:
            return None

    def process_single_segment(
        self,
        segment_file: Path,
        output_dir: Path,
        use_tja_bpm: bool = True
    ) -> Dict:
        """Process a single audio segment for beat detection."""

        try:
            # Load audio segment
            audio = np.load(segment_file)

            # Load segment metadata to get expected BPM
            song_name = segment_file.stem.split("_segment_")[0]
            expected_bpm = None

            if use_tja_bpm:
                # Try to load BPM from TJA metadata
                expected_bpm = self.load_bpm_from_tja(song_name)

            # Detect beats
            beat_results = self.detect_beats_multi_method(
                audio,
                sr=self.config["sample_rate"],
                expected_bpm=expected_bpm,
                method=self.config["detection_method"]
            )

            # Add segment metadata
            beat_results["segment_id"] = int(segment_file.stem.split("_segment_")[1])

            # Detect onsets
            onset_results = self.detect_onsets(
                audio,
                sr=self.config["sample_rate"],
                hop_length=self.config["hop_length"],
                onset_threshold=self.config["onset_threshold"]
            )

            # Add segment metadata
            onset_results["segment_id"] = beat_results["segment_id"]

            # Ensure organized output directories exist
            outputs_dir = output_dir / "outputs"
            outputs_dir.mkdir(parents=True, exist_ok=True)

            (outputs_dir / "beat_positions").mkdir(parents=True, exist_ok=True)
            (outputs_dir / "onset_positions").mkdir(parents=True, exist_ok=True)
            (outputs_dir / "tempo_analysis").mkdir(parents=True, exist_ok=True)
            (outputs_dir / "validation").mkdir(parents=True, exist_ok=True)
            if self.config["visualization"]:
                (outputs_dir / "beat_visualizations").mkdir(parents=True, exist_ok=True)

            # Save results with organized structure
            beat_file = outputs_dir / "beat_positions" / f"{segment_file.stem}.json"
            with open(beat_file, 'w') as f:
                json.dump(beat_results, f, indent=2, default=self._convert_numpy)

            onset_file = outputs_dir / "onset_positions" / f"{segment_file.stem}.json"
            with open(onset_file, 'w') as f:
                json.dump(onset_results, f, indent=2, default=self._convert_numpy)

            # Create tempo analysis
            tempo_analysis = {
                "song_name": song_name,
                "segment_id": beat_results["segment_id"],
                "estimated_tempo": beat_results["tempo"],
                "tempo_confidence": beat_results["tempo_confidence"],
                "expected_bpm": expected_bpm,
                "tempo_accuracy": None,
                "beat_count": len(beat_results["beats"]),
                "onset_count": len(onset_results["onsets"]),
                "detection_method": beat_results["detection_method"],
                "processing_metadata": beat_results["processing_metadata"]
            }

            # Calculate tempo accuracy if expected BPM available
            if expected_bpm and expected_bpm > 0:
                tempo_error = abs(beat_results["tempo"] - expected_bpm) / expected_bpm
                tempo_analysis["tempo_accuracy"] = 1.0 - tempo_error

            # Save tempo analysis
            tempo_file = outputs_dir / "tempo_analysis" / f"{segment_file.stem}.json"
            with open(tempo_file, 'w') as f:
                json.dump(tempo_analysis, f, indent=2, default=self._convert_numpy)

            # Create visualization if enabled
            if self.config["visualization"] and len(beat_results["beats"]) > 0:
                viz_file = outputs_dir / "beat_visualizations" / f"{segment_file.stem}.png"
                self.visualize_beat_detection(
                    audio, self.config["sample_rate"], beat_results, onset_results, viz_file
                )

            # Validate results
            validation_result = self._validate_segment_results(beat_results, onset_results)
            validation_file = outputs_dir / "validation" / f"{segment_file.stem}.json"
            with open(validation_file, 'w') as f:
                json.dump(validation_result, f, indent=2, default=self._convert_numpy)

            return {
                "success": True,
                "segment_file": str(segment_file),
                "beat_count": len(beat_results["beats"]),
                "onset_count": len(onset_results["onsets"]),
                "tempo": beat_results["tempo"],
                "tempo_confidence": beat_results["tempo_confidence"],
                "processing_time": beat_results["processing_metadata"]["processing_time_ms"],
                "quality_score": beat_results["processing_metadata"]["quality_score"],
                "validation_passed": validation_result["passed"]
            }

        except Exception as e:
            error_info = {
                "success": False,
                "segment_file": str(segment_file),
                "error": str(e),
                "error_type": type(e).__name__
            }
            logger.error(f"Error processing {segment_file}: {e}")
            return error_info

    def _convert_numpy(self, obj):
        """Convert numpy types to JSON serializable types."""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        else:
            return obj

    def _validate_segment_results(self, beat_results: Dict, onset_results: Dict) -> Dict:
        """Validate beat detection results for a segment."""
        validation = {
            "passed": True,
            "warnings": [],
            "errors": [],
            "quality_metrics": {}
        }

        # Check beat detection quality
        if not beat_results["beats"]:
            validation["errors"].append("No beats detected")
            validation["passed"] = False
        else:
            beat_count = len(beat_results["beats"])
            tempo_confidence = beat_results["tempo_confidence"]

            # Check minimum beat confidence
            if tempo_confidence < self.config["beat_confidence_threshold"]:
                validation["warnings"].append(
                    f"Low tempo confidence: {tempo_confidence:.3f}"
                )

            # Check beat count reasonableness
            segment_duration = beat_results["segment_duration"]
            beats_per_second = beat_count / segment_duration

            if beats_per_second < 0.5:  # Less than 30 BPM
                validation["warnings"].append(
                    f"Very low beat density: {beats_per_second:.2f} beats/sec"
                )
            elif beats_per_second > 5.0:  # More than 300 BPM
                validation["warnings"].append(
                    f"Very high beat density: {beats_per_second:.2f} beats/sec"
                )

            # Check beat interval variance
            if len(beat_results["beat_intervals"]) > 1:
                interval_variance = np.var(beat_results["beat_intervals"])
                mean_interval = np.mean(beat_results["beat_intervals"])
                relative_variance = interval_variance / (mean_interval ** 2)

                if relative_variance > self.config["max_beat_interval_variance"]:
                    validation["warnings"].append(
                        f"High beat interval variance: {relative_variance:.3f}"
                    )

        # Check onset detection quality
        if not onset_results["onsets"]:
            validation["warnings"].append("No onsets detected")
        else:
            onset_density = onset_results["onset_density"]

            # Check onset density reasonableness
            if onset_density < 1.0:  # Less than 1 onset per second
                validation["warnings"].append(
                    f"Low onset density: {onset_density:.2f} onsets/sec"
                )
            elif onset_density > 20.0:  # More than 20 onsets per second
                validation["warnings"].append(
                    f"Very high onset density: {onset_density:.2f} onsets/sec"
                )

        # Calculate quality metrics
        validation["quality_metrics"] = {
            "beat_count": len(beat_results["beats"]),
            "onset_count": len(onset_results["onsets"]),
            "tempo_confidence": beat_results["tempo_confidence"],
            "beat_density": len(beat_results["beats"]) / beat_results["segment_duration"],
            "onset_density": onset_results["onset_density"],
            "overall_quality": beat_results["processing_metadata"]["quality_score"]
        }

        return validation

    def process_beat_detection(
        self,
        input_dir: Path = Path("data/processed/phase3"),
        output_dir: Path = Path("data/processed/phase4")
    ) -> Dict:
        """Process beat detection for entire dataset."""

        logger.info("Starting Phase 4: Beat Position Estimation")

        # Setup organized output directory structure
        output_structure = {
            "outputs": [
                "beat_positions", "onset_positions", "tempo_analysis",
                "validation", "beat_visualizations"
            ],
            "reports": [],
            "checkpoints": []
        }

        for category, subdirs in output_structure.items():
            category_dir = output_dir / category
            category_dir.mkdir(parents=True, exist_ok=True)

            for subdir in subdirs:
                (category_dir / subdir).mkdir(parents=True, exist_ok=True)

        # Find all audio segments
        segment_files = list((input_dir / "audio_segments").glob("*_segment_*.npy"))

        logger.info(f"Found {len(segment_files)} audio segments to process")

        # Initialize statistics
        self.stats["total_segments"] = len(segment_files)
        all_tempos = []
        all_beat_counts = []
        tempo_errors = []
        processing_times = []
        quality_scores = []

        # Process segments with progress bar and batch processing
        batch_size = self.config.get("batch_size", 100)

        for i in tqdm(range(0, len(segment_files), batch_size), desc="Processing batches"):
            batch_files = segment_files[i:i+batch_size]

            for segment_file in tqdm(batch_files, desc=f"Batch {i//batch_size + 1}", leave=False):
                result = self.process_single_segment(
                    segment_file,
                    output_dir,
                    self.config["use_tja_bpm"]
                )

                if result["success"]:
                    self.stats["processed_segments"] += 1

                    # Collect statistics
                    if result["tempo"] > 0:
                        all_tempos.append(result["tempo"])
                        all_beat_counts.append(result["beat_count"])
                        processing_times.append(result["processing_time"])
                        quality_scores.append(result["quality_score"])

                        # Note: tempo accuracy would be calculated if TJA BPM was available

                else:
                    self.stats["failed_segments"] += 1
                    self.stats["processing_errors"].append(result)

            # Memory cleanup after each batch
            gc.collect()

            # Save intermediate progress to checkpoints directory
            if i % (batch_size * 10) == 0:  # Every 10 batches
                checkpoints_dir = output_dir / "checkpoints"
                checkpoints_dir.mkdir(parents=True, exist_ok=True)

                progress_file = checkpoints_dir / f"progress_checkpoint_{i}.json"
                checkpoint_stats = self.stats.copy()
                checkpoint_stats["current_batch"] = i // batch_size
                checkpoint_stats["total_batches"] = len(segment_files) // batch_size
                with open(progress_file, 'w') as f:
                    json.dump(checkpoint_stats, f, indent=2, default=self._convert_numpy)

        # Calculate final statistics
        if all_tempos:
            self.stats["avg_tempo"] = float(np.mean(all_tempos))
            self.stats["avg_beats_per_segment"] = float(np.mean(all_beat_counts))
            self.stats["avg_processing_time_ms"] = float(np.mean(processing_times))
            self.stats["avg_quality_score"] = float(np.mean(quality_scores))

        if tempo_errors:
            self.stats["tempo_accuracy"] = float(1.0 - np.mean(tempo_errors))

        # Save processing report to reports directory
        reports_dir = output_dir / "reports"
        reports_dir.mkdir(parents=True, exist_ok=True)

        report_file = reports_dir / "beat_tracking_report.json"
        with open(report_file, 'w') as f:
            json.dump(self.stats, f, indent=2, default=self._convert_numpy)

        # Create processing summary
        summary = {
            "phase": "Phase 4: Beat Position Estimation",
            "completion_time": time.strftime('%Y-%m-%d %H:%M:%S'),
            "total_segments": self.stats["total_segments"],
            "processed_segments": self.stats["processed_segments"],
            "success_rate": self.stats["processed_segments"] / self.stats["total_segments"] * 100,
            "avg_tempo": self.stats.get("avg_tempo", 0),
            "avg_beats_per_segment": self.stats.get("avg_beats_per_segment", 0),
            "output_structure": {
                "outputs": {
                    "beat_positions": f"{self.stats['processed_segments']} files",
                    "onset_positions": f"{self.stats['processed_segments']} files",
                    "tempo_analysis": f"{self.stats['processed_segments']} files",
                    "validation": f"{self.stats['processed_segments']} files",
                    "beat_visualizations": f"{self.stats['processed_segments']} files" if self.config["visualization"] else "disabled"
                },
                "reports": ["beat_tracking_report.json", "processing_summary.json"],
                "checkpoints": f"{len(list((output_dir / 'checkpoints').glob('*.json')))} files" if (output_dir / 'checkpoints').exists() else "0 files"
            }
        }

        summary_file = reports_dir / "processing_summary.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=self._convert_numpy)

        logger.info(f"Phase 4 completed: {self.stats['processed_segments']}/{self.stats['total_segments']} segments processed")
        logger.info(f"Average tempo: {self.stats['avg_tempo']:.1f} BPM")
        logger.info(f"Average beats per segment: {self.stats['avg_beats_per_segment']:.1f}")

        return self.stats

    def visualize_beat_detection(
        self,
        audio: np.ndarray,
        sr: int,
        beat_results: Dict,
        onset_results: Dict,
        output_path: Path
    ):
        """
        Create visualization of beat detection results.

        Generates a comprehensive visualization showing:
        - Audio waveform with detected beat markers
        - Onset strength function with detected onsets
        - Beat interval analysis for tempo consistency

        Args:
            audio: Input audio segment
            sr: Sample rate
            beat_results: Beat detection results
            onset_results: Onset detection results
            output_path: Path to save visualization
        """
        try:
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 10))

            # Plot 1: Waveform with beats (downsample for performance)
            downsample_factor = max(1, len(audio) // 10000)  # Limit to ~10k points
            time_ds = np.linspace(0, len(audio)/sr, len(audio))[::downsample_factor]
            audio_ds = audio[::downsample_factor]

            ax1.plot(time_ds, audio_ds, alpha=0.7, color='blue', linewidth=0.5)

            # Mark detected beats with red vertical lines (use vlines for efficiency)
            beat_times = [beat["time"] for beat in beat_results["beats"]]
            if len(beat_times) > 0:
                ax1.vlines(beat_times, ymin=np.min(audio_ds), ymax=np.max(audio_ds),
                          colors='red', alpha=0.8, linewidth=1.5)

            ax1.set_title(f'Audio Waveform with Detected Beats (Tempo: {beat_results["tempo"]:.1f} BPM)')
            ax1.set_xlabel('Time (seconds)')
            ax1.set_ylabel('Amplitude')
            ax1.grid(True, alpha=0.3)

            # Plot 2: Onset strength function
            onset_envelope = librosa.onset.onset_strength(y=audio, sr=sr, hop_length=512)
            onset_times = librosa.frames_to_time(np.arange(len(onset_envelope)), sr=sr, hop_length=512)

            ax2.plot(onset_times, onset_envelope, color='green', linewidth=2)

            # Mark detected onsets (use vlines for efficiency)
            onset_times_detected = [onset["time"] for onset in onset_results["onsets"]]
            if len(onset_times_detected) > 0:
                ax2.vlines(onset_times_detected, ymin=0, ymax=np.max(onset_envelope),
                          colors='orange', alpha=0.6, linewidth=1)

            ax2.set_title('Onset Strength Function with Detected Onsets')
            ax2.set_xlabel('Time (seconds)')
            ax2.set_ylabel('Onset Strength')
            ax2.grid(True, alpha=0.3)

            # Plot 3: Beat intervals analysis
            if len(beat_results["beat_intervals"]) > 0:
                ax3.plot(beat_results["beat_intervals"], 'o-', color='purple', markersize=4)
                mean_interval = np.mean(beat_results["beat_intervals"])
                ax3.axhline(mean_interval, color='red', linestyle='--')

                # Add tempo confidence and mean interval information
                tempo_conf = beat_results.get("tempo_confidence", 0.0)
                info_text = f'Tempo Confidence: {tempo_conf:.3f}\nMean Interval: {mean_interval:.3f}s'
                ax3.text(0.02, 0.98, info_text,
                        transform=ax3.transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

                ax3.set_title('Beat Intervals Analysis')
                ax3.set_xlabel('Beat Number')
                ax3.set_ylabel('Interval (seconds)')
                ax3.grid(True, alpha=0.3)
            else:
                ax3.text(0.5, 0.5, 'No beat intervals to display',
                        ha='center', va='center', transform=ax3.transAxes,
                        fontsize=14, color='gray')
                ax3.set_title('Beat Intervals Analysis')

            # Add overall information
            fig.suptitle(f'Beat Detection Analysis - {output_path.stem}', fontsize=16, fontweight='bold')

            # Add metadata text
            metadata_text = (
                f"Detection Method: {beat_results.get('detection_method', 'unknown')}\n"
                f"Segment Duration: {beat_results.get('segment_duration', 0):.1f}s\n"
                f"Beats Detected: {len(beat_results['beats'])}\n"
                f"Onsets Detected: {len(onset_results['onsets'])}\n"
                f"Quality Score: {beat_results.get('processing_metadata', {}).get('quality_score', 0):.3f}"
            )

            fig.text(0.02, 0.02, metadata_text, fontsize=10,
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

            # Use subplots_adjust instead of tight_layout for better performance
            plt.subplots_adjust(left=0.1, right=0.95, top=0.95, bottom=0.15, hspace=0.4)
            plt.savefig(output_path, dpi=150, bbox_inches='tight')
            plt.close()

        except Exception as e:
            logger.warning(f"Failed to create visualization for {output_path}: {e}")
            # Create a simple error visualization
            try:
                fig, ax = plt.subplots(1, 1, figsize=(10, 6))
                ax.text(0.5, 0.5, f'Visualization Error:\n{str(e)}',
                       ha='center', va='center', transform=ax.transAxes,
                       fontsize=12, color='red')
                ax.set_title(f'Beat Detection Visualization Error - {output_path.stem}')
                plt.savefig(output_path, dpi=150, bbox_inches='tight')
                plt.close()
            except:
                pass  # If even error visualization fails, just skip




def main():
    """Main execution function for Phase 4."""

    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Initialize beat estimator
    estimator = Phase4BeatEstimator()

    # Process beat detection
    try:
        results = estimator.process_beat_detection()

        print("\n" + "="*60)
        print("PHASE 4: BEAT POSITION ESTIMATION - COMPLETED")
        print("="*60)
        print(f"Total segments processed: {results['processed_segments']}/{results['total_segments']}")
        print(f"Success rate: {results['processed_segments']/results['total_segments']*100:.1f}%")
        print(f"Average tempo: {results['avg_tempo']:.1f} BPM")
        print(f"Average beats per segment: {results['avg_beats_per_segment']:.1f}")

        if results['processing_errors']:
            print(f"Errors encountered: {len(results['processing_errors'])}")

        print("\nOutput files generated:")
        print("- data/processed/phase4/outputs/beat_positions/*.json")
        print("- data/processed/phase4/outputs/onset_positions/*.json")
        print("- data/processed/phase4/outputs/tempo_analysis/*.json")
        print("- data/processed/phase4/outputs/validation/*.json")
        if self.config["visualization"]:
            print("- data/processed/phase4/outputs/beat_visualizations/*.png")
        print("- data/processed/phase4/reports/beat_tracking_report.json")
        print("- data/processed/phase4/reports/processing_summary.json")

    except Exception as e:
        logger.error(f"Phase 4 processing failed: {e}")
        raise


if __name__ == "__main__":
    main()
