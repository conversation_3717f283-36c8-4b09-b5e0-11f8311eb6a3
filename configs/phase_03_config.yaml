# Phase 3: Silence Detection & Audio Segmentation Configuration
# Optimized implementation with adaptive quality thresholds and performance modes

# Input/Output Paths (Windows format)
paths:
  input_audio: "data\\processed\\phase2\\filtered_audio"
  input_metadata: "data\\processed\\phase2\\filtered_metadata"
  input_quality_metrics: "data\\processed\\phase2\\quality_metrics"
  output_root: "data\\processed\\phase3"
  silence_maps: "data\\processed\\phase3\\silence_maps"
  audio_segments: "data\\processed\\phase3\\audio_segments"
  energy_profiles: "data\\processed\\phase3\\energy_profiles"
  logs_output: "data\\processed\\phase3\\processing_logs"

# Silence Detection Parameters (Optimized)
silence_detection:
  # Performance mode
  fast_mode: false                 # Enable fast processing mode (6.4x speedup)

  # Energy-based detection
  silence_threshold: -40.0         # Base silence threshold (dB)
  adaptive_threshold: true         # Use adaptive thresholding
  percentile_threshold: 20         # Percentile for adaptive threshold
  threshold_offset: -10.0          # Offset from adaptive threshold

  # Multi-band analysis
  use_multiband: true              # Enable multi-band analysis
  band_threshold_offset: 5.0       # Additional offset for frequency bands

  # Temporal constraints
  min_silence_duration: 0.5        # Minimum silence duration (seconds)
  min_music_duration: 1.0          # Minimum music duration (seconds)

  # Analysis parameters
  frame_length: 2048               # Frame size for analysis
  hop_length: 512                  # Hop size for analysis

  # Morphological operations
  apply_smoothing: true            # Apply temporal smoothing
  opening_kernel_ratio: 0.8        # Opening kernel size ratio
  closing_kernel_ratio: 1.2        # Closing kernel size ratio

# Audio Segmentation Parameters
segmentation:
  padding: 0.1                     # Padding around segments (seconds)
  max_segment_duration: 300.0      # Maximum segment duration (seconds)
  merge_short_gaps: true           # Merge segments with short gaps
  max_gap_duration: 2.0            # Maximum gap to merge (seconds)
  energy_profile_hop: 512          # Hop length for energy profiles

# Quality Validation (Maximum Permissive for Production)
quality_thresholds:
  min_music_percentage: 0.0        # Accept any content (0%)
  max_silence_percentage: 100.0    # Accept any content (100%)
  min_segments_per_file: 0         # Allow completely silent tracks
  max_segments_per_file: 100       # Allow extremely complex songs
  min_segment_duration: 0.0        # Allow any duration

  # Adaptive thresholds (maximally permissive)
  complex_song_threshold: 1         # Very low threshold
  complex_music_percentage: 0.0     # Accept any content
  complex_silence_percentage: 100.0 # Accept any content
  long_song_duration: 60.0          # Duration threshold for long songs (1 minute)
  long_song_max_segments: 200       # Allow extremely complex segmentation

# Memory Management (RTX 3070 constraints)
memory:
  max_ram_usage_gb: 6.5            # Maximum RAM usage (6.5GB limit)
  max_files_in_memory: 12          # Maximum files to process simultaneously
  chunk_size: 1024                 # Audio chunk size for processing
  gc_frequency: 5                  # Garbage collection frequency (every 5 files)
  clear_cache_frequency: 20        # Clear audio cache frequency

# Processing Parameters (Optimized)
processing:
  max_workers: 2                   # Parallel processing workers
  batch_size: 6                    # Files per batch (optimized for memory)
  validation_subset_size: 50       # Files for initial validation
  timeout_seconds: 90              # Timeout per file processing
  retry_attempts: 2                # Retry attempts for failed files

# Quality Gates (Maintained Standards)
quality_gates:
  min_success_rate: 0.95           # Minimum 95% success rate
  min_quality_pass_rate: 0.90      # Minimum 90% quality pass rate
  min_processed_files: 50          # Minimum number of files that must be processed

# Logging Configuration
logging:
  level: "INFO"                    # Logging level (DEBUG, INFO, WARNING, ERROR)
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file_encoding: "utf-8"           # Unicode support for file paths
  max_log_size_mb: 100             # Maximum log file size
  backup_count: 5                  # Number of backup log files

# Error Handling
error_handling:
  continue_on_error: true          # Continue processing if individual files fail
  save_error_details: true         # Save detailed error information
  create_error_report: true        # Generate error summary report

# Output Format
output:
  save_silence_maps: true          # Save silence detection results
  save_audio_segments: true        # Save segmented audio files
  save_energy_profiles: true       # Save energy profiles
  compress_arrays: false           # Compress numpy arrays (slower but smaller)
  metadata_indent: 2               # JSON indentation for readability

# Validation Settings
validation:
  run_validation: true             # Run validation after processing
  validate_file_integrity: true    # Check file integrity
  validate_segment_quality: true   # Check segment quality
  generate_statistics: true        # Generate processing statistics
  create_segmentation_report: true # Create comprehensive segmentation report
  visualize_results: false         # Generate visualization plots (memory intensive)

# Note: Detection algorithm weights are currently not implemented
# The multi-band analysis uses equal weighting for all frequency bands
# These settings are reserved for future algorithm enhancements

