# Phase 3: Silence Detection & Audio Segmentation Configuration
# Windows-compatible paths with RTX 3070 memory management

# Input/Output Paths (Windows format)
paths:
  input_audio: "data\\processed\\phase2\\filtered_audio"
  input_metadata: "data\\processed\\phase2\\filtered_metadata"
  input_quality: "data\\processed\\phase2\\quality_metrics"
  quality_report: "data\\processed\\phase2\\quality_report.json"
  output_root: "data\\processed\\phase3"
  silence_maps: "data\\processed\\phase3\\silence_maps"
  audio_segments: "data\\processed\\phase3\\audio_segments"
  energy_profiles: "data\\processed\\phase3\\energy_profiles"
  logs_output: "data\\processed\\phase3\\processing_logs"

# Audio Processing Parameters
audio:
  sample_rate: 22050
  frame_length: 2048
  hop_length: 512
  n_fft: 2048

# Silence Detection Parameters
silence_detection:
  silence_threshold_db: -40.0      # Base silence threshold in dB
  min_silence_duration: 0.5        # Minimum silence duration in seconds
  min_music_duration: 1.0          # Minimum music duration in seconds
  adaptive_threshold: true         # Use adaptive thresholding
  percentile_threshold: 20         # Percentile for adaptive threshold
  threshold_offset: -10            # Offset from adaptive threshold
  
# Multi-band Analysis
multiband:
  enable: true
  low_band_ratio: 0.33            # Low frequency band (0 to 33% of spectrum)
  mid_band_ratio: 0.67            # Mid frequency band (33% to 67% of spectrum)
  band_threshold_offset: 5        # Additional threshold offset for bands

# Morphological Operations
morphology:
  enable: true
  opening_kernel_ratio: 1.0       # Kernel size as ratio of min_silence_duration
  closing_kernel_ratio: 1.0       # Kernel size as ratio of min_silence_duration

# Audio Segmentation
segmentation:
  padding_seconds: 0.1            # Padding around segments in seconds
  merge_short_gaps: true          # Merge segments with short gaps
  max_gap_duration: 0.3           # Maximum gap to merge in seconds
  min_segment_energy: 0.001       # Minimum energy for valid segments

# Memory Management (RTX 3070 constraints)
memory:
  max_vram_usage_gb: 7.0
  max_ram_usage_gb: 6.0
  batch_size: 32                  # Files to process in batch
  enable_cleanup: true            # Enable explicit memory cleanup
  cleanup_interval: 50            # Clean up every N files

# Processing Configuration
processing:
  max_workers: 2                  # Parallel workers
  timeout_seconds: 300            # Timeout per file
  retry_attempts: 2               # Retry failed files
  checkpoint_interval: 100        # Save checkpoint every N files
  enable_progress_bar: true

# Quality Gates
quality_gates:
  min_success_rate: 0.85          # Minimum processing success rate
  min_detection_accuracy: 0.85    # Minimum silence detection accuracy
  max_boundary_error_seconds: 0.2 # Maximum boundary precision error
  min_segments_per_file: 1        # Minimum segments per file
  max_segments_per_file: 10       # Maximum segments per file
  min_processing_speed: 50        # Minimum files per minute

# Validation
validation:
  enable_unit_tests: true
  enable_visual_validation: false  # Disable for batch processing
  sample_validation_files: 10     # Number of files for validation
  generate_statistics: true
  save_debug_info: false

# Output Configuration
output:
  save_silence_maps: true
  save_audio_segments: true
  save_energy_profiles: true
  save_segment_metadata: true
  compress_arrays: true           # Compress numpy arrays
  metadata_indent: 2
  create_summary_report: true

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file_encoding: "utf-8"
  max_log_size_mb: 100
  backup_count: 5

# Error Handling
error_handling:
  continue_on_error: true
  save_error_details: true
  create_error_report: true
  max_consecutive_errors: 10
