# Phase 5: Tempo Alignment & BPM Validation Configuration

# Input/Output Directories
input_dir: "data/processed/phase4/outputs"
output_dir: "data/processed/phase5"
tja_data_dir: "data/raw/ese"

# Tempo Alignment Parameters
bpm_tolerance: 0.05  # 5% tolerance for BPM validation
phase_optimization_points: 50  # Number of phase offset points to test
tempo_change_threshold: 0.1  # 10% change threshold for tempo change detection
tempo_change_window_size: 8  # Number of beats in tempo change analysis window

# Validation Thresholds
max_alignment_error_ms: 100  # Maximum acceptable alignment error in milliseconds
min_confidence_threshold: 0.7  # Minimum confidence for beat alignment
segment_consistency_threshold: 0.9  # Minimum segment consistency for validation

# Processing Options
create_visualizations: true  # Generate alignment visualization plots
save_timing_analysis: true  # Save detailed timing analysis
enable_robust_estimation: true  # Use RANSAC for robust tempo estimation
default_bpm: 120.0  # Default BPM when estimation fails

# Output Format Options
include_beat_grid: true  # Include detailed beat grid in output
include_tempo_changes: true  # Include tempo change detection results
include_alignment_corrections: true  # Include timing corrections applied

# Quality Control
min_beats_per_song: 10  # Minimum number of beats required for processing
max_tempo_drift_percent: 20.0  # Maximum acceptable tempo drift percentage
validation_pass_threshold: 85.0  # Minimum percentage of songs that should pass validation

# Logging Configuration
log_level: "INFO"
log_detailed_errors: true
log_processing_stats: true

# Memory Management
batch_size: 100  # Number of songs to process in each batch
clear_cache_interval: 50  # Clear memory cache every N songs

# Visualization Settings
plot_width: 15
plot_height: 8
plot_dpi: 150
save_plots: true
plot_format: "png"

# Advanced Settings
use_weighted_averaging: true  # Use confidence-weighted averaging for BPM estimation
enable_phase_correction: true  # Enable phase offset correction
adaptive_tolerance: false  # Adapt tolerance based on song characteristics
