import numpy as np
import sys
sys.path.append('src')
from utils.silence_utils import detect_silence_regions
import librosa

# Create test audio: 2s music + 1s silence + 2s music
sr = 22050
music1 = 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, 2, 2*sr))
silence = np.zeros(int(1*sr))
music2 = 0.5 * np.sin(2 * np.pi * 880 * np.linspace(0, 2, 2*sr))
audio = np.concatenate([music1, silence, music2])

print(f'Audio shape: {audio.shape}')
print(f'Audio duration: {len(audio)/sr:.1f}s')
print(f'Music1 max: {np.max(np.abs(music1)):.3f}')
print(f'Silence max: {np.max(np.abs(silence)):.3f}')
print(f'Music2 max: {np.max(np.abs(music2)):.3f}')

# Calculate RMS energy to debug
rms_energy = librosa.feature.rms(y=audio, frame_length=2048, hop_length=512)[0]
rms_db = librosa.amplitude_to_db(rms_energy, ref=np.max)
print(f'RMS energy shape: {rms_energy.shape}')
print(f'RMS dB range: {np.min(rms_db):.1f} to {np.max(rms_db):.1f}')
print(f'RMS dB mean: {np.mean(rms_db):.1f}')

# Check frame times and find silence section
frame_times = librosa.frames_to_time(np.arange(len(rms_energy)), sr=22050, hop_length=512)
silence_start_frame = int(2.0 * 22050 / 512)  # Around 2 seconds
silence_end_frame = int(3.0 * 22050 / 512)    # Around 3 seconds
print(f'Expected silence frames: {silence_start_frame} to {silence_end_frame}')
print(f'RMS dB in silence region: {rms_db[silence_start_frame:silence_end_frame]}')
print(f'Min RMS dB in silence: {np.min(rms_db[silence_start_frame:silence_end_frame]):.1f}')

# Test silence detection with debug
print("\nTesting with different thresholds:")
for thresh in [-20, -30, -40, -50, -70]:
    result = detect_silence_regions(audio, sr, silence_threshold=thresh, min_silence_duration=0.3)
    print(f'Threshold {thresh}dB: {len(result["silence_regions"])} silence, {len(result["music_regions"])} music')

# Test with non-adaptive settings
result = detect_silence_regions(
    audio, sr,
    silence_threshold=-50,
    adaptive_threshold=False,
    min_silence_duration=0.3,
    min_music_duration=0.5,
    apply_smoothing=True,
    use_multiband=False
)
print(f'\nPermissive settings:')
print(f'Silence regions: {len(result["silence_regions"])}')
print(f'Music regions: {len(result["music_regions"])}')
print(f'Silence %: {result["silence_percentage"]:.1f}%')
print(f'Music %: {result["music_percentage"]:.1f}%')

if result["silence_regions"]:
    for i, region in enumerate(result["silence_regions"]):
        print(f'  Silence {i}: {region["start"]:.2f}s - {region["end"]:.2f}s')

if result["music_regions"]:
    for i, region in enumerate(result["music_regions"]):
        print(f'  Music {i}: {region["start"]:.2f}s - {region["end"]:.2f}s')
