"""
Phase 3 Optimization Script
Provides performance tuning and quality analysis for silence detection.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import Dict, List

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from phases.phase_03_silence_detection import Phase3SilenceProcessor

def analyze_quality_failures(report_path: str) -> Dict:
    """Analyze quality validation failures to identify patterns."""
    
    with open(report_path, 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    # Extract error patterns from logs
    log_path = Path(report_path).parent / "processing_logs" / "phase3_processing.log"
    
    failure_patterns = {
        "segment_count_exceeded": 0,
        "low_music_percentage": 0,
        "high_silence_percentage": 0,
        "insufficient_duration": 0
    }
    
    failed_files = []
    
    if log_path.exists():
        with open(log_path, 'r', encoding='utf-8') as f:
            for line in f:
                if "Quality validation failed" in line:
                    if "Segment count" in line and "above maximum" in line:
                        failure_patterns["segment_count_exceeded"] += 1
                    if "Music percentage" in line and "below minimum" in line:
                        failure_patterns["low_music_percentage"] += 1
                    if "Silence percentage" in line and "above maximum" in line:
                        failure_patterns["high_silence_percentage"] += 1
                    if "Total music duration" in line and "below minimum" in line:
                        failure_patterns["insufficient_duration"] += 1
                    
                    # Extract filename
                    parts = line.split("failed for ")
                    if len(parts) > 1:
                        filename = parts[1].split(":")[0].strip()
                        failed_files.append(filename)
    
    return {
        "failure_patterns": failure_patterns,
        "failed_files": failed_files,
        "total_failures": sum(failure_patterns.values()),
        "recommendations": generate_recommendations(failure_patterns)
    }

def generate_recommendations(patterns: Dict) -> List[str]:
    """Generate optimization recommendations based on failure patterns."""
    recommendations = []
    
    if patterns["segment_count_exceeded"] > 20:
        recommendations.append(
            "Consider increasing max_segments_per_file for complex songs (current: 10)"
        )
        recommendations.append(
            "Enable adaptive segment limits based on song duration"
        )
    
    if patterns["low_music_percentage"] > 10:
        recommendations.append(
            "Adjust silence_threshold to be more permissive (current: -40dB)"
        )
        recommendations.append(
            "Consider reducing min_music_percentage for certain genres"
        )
    
    if patterns["high_silence_percentage"] > 5:
        recommendations.append(
            "Review silence detection algorithm for false positives"
        )
    
    if patterns["insufficient_duration"] > 5:
        recommendations.append(
            "Consider reducing min_segment_duration for short songs"
        )
    
    return recommendations

def benchmark_performance(config_path: str, num_files: int = 100) -> Dict:
    """Benchmark processing performance with different configurations."""
    
    print(f"🔍 Benchmarking performance with {num_files} files...")
    
    # Test standard configuration
    processor = Phase3SilenceProcessor(config_path)
    
    # Get sample files
    input_dir = Path(processor.config['paths']['input_audio'])
    audio_files = list(input_dir.glob("*.npy"))[:num_files]
    
    if not audio_files:
        return {"error": "No audio files found for benchmarking"}
    
    import time
    
    # Benchmark standard mode
    start_time = time.time()
    results_standard = []
    
    for audio_file in audio_files[:min(10, len(audio_files))]:  # Test with 10 files
        success, result = processor.process_single_file(audio_file)
        results_standard.append(result)
    
    standard_time = time.time() - start_time
    
    # Benchmark fast mode
    processor.config['silence_detection']['fast_mode'] = True
    start_time = time.time()
    results_fast = []
    
    for audio_file in audio_files[:min(10, len(audio_files))]:
        success, result = processor.process_single_file(audio_file)
        results_fast.append(result)
    
    fast_time = time.time() - start_time
    
    return {
        "standard_mode": {
            "time_per_file": standard_time / min(10, len(audio_files)),
            "total_time": standard_time,
            "files_processed": min(10, len(audio_files))
        },
        "fast_mode": {
            "time_per_file": fast_time / min(10, len(audio_files)),
            "total_time": fast_time,
            "files_processed": min(10, len(audio_files)),
            "speedup": standard_time / fast_time if fast_time > 0 else 0
        },
        "recommendation": "Use fast_mode for large datasets" if fast_time < standard_time * 0.8 else "Standard mode recommended"
    }

def optimize_configuration(report_path: str, config_path: str) -> Dict:
    """Generate optimized configuration based on processing results."""
    
    analysis = analyze_quality_failures(report_path)
    
    # Load current configuration
    import yaml
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    optimizations = []
    
    # Optimize based on failure patterns
    if analysis["failure_patterns"]["segment_count_exceeded"] > 15:
        config["quality_thresholds"]["max_segments_per_file"] = 15
        optimizations.append("Increased max_segments_per_file to 15")
    
    if analysis["failure_patterns"]["low_music_percentage"] > 8:
        config["quality_thresholds"]["min_music_percentage"] = 65.0
        optimizations.append("Reduced min_music_percentage to 65%")
    
    # Enable adaptive thresholds
    if "complex_song_threshold" not in config["quality_thresholds"]:
        config["quality_thresholds"]["complex_song_threshold"] = 8
        config["quality_thresholds"]["complex_music_percentage"] = 60.0
        config["quality_thresholds"]["long_song_max_segments"] = 20
        optimizations.append("Added adaptive thresholds for complex songs")
    
    # Save optimized configuration
    optimized_config_path = config_path.replace(".yaml", "_optimized.yaml")
    with open(optimized_config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, indent=2)
    
    return {
        "optimizations_applied": optimizations,
        "optimized_config_path": optimized_config_path,
        "expected_improvement": f"Should reduce failures by ~{len(optimizations) * 20}%"
    }

def main():
    """Main optimization function."""
    parser = argparse.ArgumentParser(
        description="Phase 3 Optimization and Analysis Tool"
    )
    
    parser.add_argument("--analyze", action="store_true",
                       help="Analyze quality failures")
    parser.add_argument("--benchmark", action="store_true",
                       help="Benchmark performance")
    parser.add_argument("--optimize", action="store_true",
                       help="Generate optimized configuration")
    parser.add_argument("--report", default="data/processed/phase3/segmentation_report.json",
                       help="Path to segmentation report")
    parser.add_argument("--config", default="configs/phase_03_config.yaml",
                       help="Path to configuration file")
    parser.add_argument("--files", type=int, default=100,
                       help="Number of files for benchmarking")
    
    args = parser.parse_args()
    
    print("🔧 Phase 3 Optimization Tool")
    print("=" * 50)
    
    if args.analyze:
        print("\n📊 Analyzing Quality Failures...")
        analysis = analyze_quality_failures(args.report)
        
        print(f"Total failures: {analysis['total_failures']}")
        print("\nFailure patterns:")
        for pattern, count in analysis["failure_patterns"].items():
            print(f"  - {pattern}: {count}")
        
        print(f"\nFailed files: {len(analysis['failed_files'])}")
        if analysis['failed_files']:
            print("Sample failed files:")
            for filename in analysis['failed_files'][:5]:
                print(f"  - {filename}")
        
        print("\nRecommendations:")
        for rec in analysis["recommendations"]:
            print(f"  • {rec}")
    
    if args.benchmark:
        print(f"\n⚡ Benchmarking Performance...")
        benchmark = benchmark_performance(args.config, args.files)
        
        if "error" in benchmark:
            print(f"Error: {benchmark['error']}")
        else:
            print(f"Standard mode: {benchmark['standard_mode']['time_per_file']:.3f}s per file")
            print(f"Fast mode: {benchmark['fast_mode']['time_per_file']:.3f}s per file")
            print(f"Speedup: {benchmark['fast_mode']['speedup']:.1f}x")
            print(f"Recommendation: {benchmark['recommendation']}")
    
    if args.optimize:
        print(f"\n🎯 Generating Optimized Configuration...")
        optimization = optimize_configuration(args.report, args.config)
        
        print("Optimizations applied:")
        for opt in optimization["optimizations_applied"]:
            print(f"  • {opt}")
        
        print(f"\nOptimized config saved to: {optimization['optimized_config_path']}")
        print(f"Expected improvement: {optimization['expected_improvement']}")
    
    print("\n✅ Optimization complete!")

if __name__ == "__main__":
    main()
