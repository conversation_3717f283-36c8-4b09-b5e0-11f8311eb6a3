#!/usr/bin/env python3
"""
Integration test script for Phase 5: Tempo Alignment & BPM Validation

This script tests the Phase 5 implementation with a small subset of data
to verify it works correctly with the actual project structure.
"""

import sys
import json
import tempfile
import shutil
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from phases.phase_05_tempo_alignment import TempoAlignmentProcessor


def create_test_data():
    """Create test data structure for integration testing."""
    temp_dir = Path(tempfile.mkdtemp())
    print(f"Created test directory: {temp_dir}")
    
    # Create input directory structure
    input_dir = temp_dir / "input"
    beat_positions_dir = input_dir / "beat_positions"
    beat_positions_dir.mkdir(parents=True)
    
    # Create TJA directory structure
    tja_dir = temp_dir / "tja" / "test_category"
    tja_dir.mkdir(parents=True)
    
    # Create test beat position files
    test_songs = [
        {"name": "TestSong1", "bpm": 120.0, "offset": 0.0},
        {"name": "TestSong2", "bpm": 140.0, "offset": -1.5},
        {"name": "TestSong3", "bpm": 160.0, "offset": 0.5}
    ]
    
    for song in test_songs:
        # Create TJA file
        tja_content = f"""TITLE:{song['name']}
BPM:{song['bpm']}
OFFSET:{song['offset']}
WAVE:{song['name']}.ogg

COURSE:Oni
LEVEL:8

#START
1000,
2000,
#END
"""
        tja_file = tja_dir / f"{song['name']}.tja"
        with open(tja_file, 'w', encoding='utf-8') as f:
            f.write(tja_content)
        
        # Create beat position files (simulate segments)
        for segment in range(2):
            # Generate synthetic beat positions
            interval = 60.0 / song['bpm']
            beat_times = [i * interval + song['offset'] for i in range(16)]  # 16 beats
            
            beat_data = {
                "song_name": song['name'],
                "segment_id": segment,
                "tempo": song['bpm'] + (segment * 2),  # Slight variation
                "beats": [
                    {
                        "time": time,
                        "confidence": 0.8 + (i % 3) * 0.1,
                        "strength": 0.7 + (i % 4) * 0.1,
                        "beat_number": i,
                        "measure_position": float(i % 4) / 4.0
                    }
                    for i, time in enumerate(beat_times)
                ]
            }
            
            beat_file = beat_positions_dir / f"{song['name']}_segment_{segment}.json"
            with open(beat_file, 'w') as f:
                json.dump(beat_data, f, indent=2)
    
    return temp_dir, input_dir, tja_dir


def test_phase_05_integration():
    """Test Phase 5 integration with realistic data."""
    print("Starting Phase 5 integration test...")
    
    # Create test data
    temp_dir, input_dir, tja_dir = create_test_data()
    output_dir = temp_dir / "output"
    
    try:
        # Initialize processor
        processor = TempoAlignmentProcessor(
            input_dir=input_dir,
            output_dir=output_dir,
            tja_data_dir=tja_dir,
            bpm_tolerance=0.05
        )
        
        print("Processor initialized successfully")
        
        # Test TJA metadata loading
        print("\nTesting TJA metadata loading...")
        for song_name in ["TestSong1", "TestSong2", "TestSong3"]:
            bpm, offset = processor.load_tja_metadata(song_name)
            print(f"  {song_name}: BPM={bpm}, OFFSET={offset}")
            assert bpm is not None, f"Failed to load BPM for {song_name}"
        
        # Run full processing
        print("\nRunning Phase 5 processing...")
        results = processor.process_tempo_alignment()
        
        # Verify results
        print("\nVerifying results...")
        print(f"Total songs: {results['total_songs']}")
        print(f"Processed songs: {results['processed_songs']}")
        print(f"Validation passed: {results['validation_passed']}")
        print(f"Validation failed: {results['validation_failed']}")
        print(f"Processing errors: {len(results['processing_errors'])}")
        
        # Check output files exist
        expected_dirs = ["tempo_alignment", "bpm_validation", "aligned_beats", "timing_analysis"]
        for dir_name in expected_dirs:
            dir_path = output_dir / dir_name
            assert dir_path.exists(), f"Output directory {dir_name} not created"
            files = list(dir_path.glob("*.json"))
            print(f"  {dir_name}: {len(files)} files created")
        
        # Verify specific output files
        alignment_files = list((output_dir / "tempo_alignment").glob("*.json"))
        if alignment_files:
            with open(alignment_files[0], 'r') as f:
                alignment_data = json.load(f)
            
            required_keys = ["aligned_bpm", "bpm_confidence", "tempo_drift", 
                           "alignment_offset", "beat_grid", "tempo_changes"]
            for key in required_keys:
                assert key in alignment_data, f"Missing key {key} in alignment output"
            
            print(f"  Sample alignment data keys: {list(alignment_data.keys())}")
        
        validation_files = list((output_dir / "bpm_validation").glob("*.json"))
        if validation_files:
            with open(validation_files[0], 'r') as f:
                validation_data = json.load(f)
            
            required_keys = ["tja_bpm", "detected_bpm", "bpm_error", 
                           "bpm_error_percentage", "validation_passed"]
            for key in required_keys:
                assert key in validation_data, f"Missing key {key} in validation output"
            
            print(f"  Sample validation data keys: {list(validation_data.keys())}")
        
        print("\n✅ Phase 5 integration test PASSED!")
        return True
        
    except Exception as e:
        print(f"\n❌ Phase 5 integration test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        print(f"Cleaned up test directory: {temp_dir}")


def test_individual_components():
    """Test individual components of Phase 5."""
    print("\nTesting individual components...")
    
    temp_dir = Path(tempfile.mkdtemp())
    
    try:
        processor = TempoAlignmentProcessor(
            input_dir=temp_dir / "input",
            output_dir=temp_dir / "output",
            tja_data_dir=temp_dir / "tja",
            bpm_tolerance=0.05
        )
        
        # Test tempo alignment with synthetic data
        print("  Testing tempo alignment...")
        beat_positions = [
            {"time": i * 0.5, "confidence": 0.9, "strength": 0.8}
            for i in range(16)
        ]
        
        result = processor.align_tempo_with_tja(beat_positions, 120.0, 0.0)
        assert "aligned_bpm" in result
        assert "beat_grid" in result
        assert len(result["beat_grid"]) == len(beat_positions)
        print("    ✅ Tempo alignment works")
        
        # Test BPM validation
        print("  Testing BPM validation...")
        validation = processor.validate_bpm_alignment(122.0, 120.0, [121.0, 122.0, 123.0])
        assert "validation_passed" in validation
        assert "bpm_error_percentage" in validation
        print("    ✅ BPM validation works")
        
        # Test aligned beats output
        print("  Testing aligned beats output...")
        aligned_beats = processor.create_aligned_beats_output(result, beat_positions)
        assert len(aligned_beats) == len(result["beat_grid"])
        if aligned_beats:
            required_fields = ["beat_id", "beat_time", "original_time", "confidence"]
            for field in required_fields:
                assert field in aligned_beats[0], f"Missing field {field}"
        print("    ✅ Aligned beats output works")
        
        print("  ✅ All individual components work correctly")
        return True
        
    except Exception as e:
        print(f"  ❌ Component test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


def main():
    """Run all integration tests."""
    print("=" * 60)
    print("Phase 5: Tempo Alignment & BPM Validation - Integration Tests")
    print("=" * 60)
    
    success = True
    
    # Test individual components
    success &= test_individual_components()
    
    # Test full integration
    success &= test_phase_05_integration()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED! Phase 5 is ready for use.")
    else:
        print("❌ SOME TESTS FAILED! Please check the implementation.")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
