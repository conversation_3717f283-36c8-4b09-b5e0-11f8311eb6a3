#!/usr/bin/env python3
"""
Script to run Phase 5: Tempo Alignment & BPM Validation

This script processes the entire dataset from Phase 4, aligning detected beats
with TJA BPM references and validating tempo consistency.

Usage:
    python scripts/run_phase_05.py [--config CONFIG_FILE] [--tolerance TOLERANCE]
"""

import sys
import argparse
import yaml
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from phases.phase_05_tempo_alignment import run_phase_05, TempoAlignmentProcessor


def load_config(config_path: str) -> dict:
    """Load configuration from YAML file."""
    try:
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"Error loading config: {e}")
        return {}


def main():
    parser = argparse.ArgumentParser(description="Run Phase 5: Tempo Alignment & BPM Validation")
    parser.add_argument("--config", default="configs/phase_05_config.yaml",
                       help="Path to configuration file")
    parser.add_argument("--tolerance", type=float, default=None,
                       help="BPM tolerance (overrides config)")
    parser.add_argument("--input-dir", default=None,
                       help="Input directory (overrides config)")
    parser.add_argument("--output-dir", default=None,
                       help="Output directory (overrides config)")
    parser.add_argument("--tja-dir", default=None,
                       help="TJA data directory (overrides config)")
    parser.add_argument("--visualize", action="store_true",
                       help="Create visualization plots")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Override config with command line arguments
    input_dir = args.input_dir or config.get("input_dir", "data/processed/phase4/outputs")
    output_dir = args.output_dir or config.get("output_dir", "data/processed/phase5")
    tja_data_dir = args.tja_dir or config.get("tja_data_dir", "data/raw/ese")
    bpm_tolerance = args.tolerance or config.get("bpm_tolerance", 0.05)
    
    print("=" * 60)
    print("Phase 5: Tempo Alignment & BPM Validation")
    print("=" * 60)
    print(f"Input directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    print(f"TJA data directory: {tja_data_dir}")
    print(f"BPM tolerance: {bpm_tolerance * 100:.1f}%")
    print(f"Configuration: {args.config}")
    print("=" * 60)
    
    try:
        # Run Phase 5 processing
        results = run_phase_05(
            input_dir=input_dir,
            output_dir=output_dir,
            tja_data_dir=tja_data_dir,
            bpm_tolerance=bpm_tolerance
        )
        
        # Print results summary
        print("\n" + "=" * 60)
        print("PHASE 5 RESULTS SUMMARY")
        print("=" * 60)
        print(f"Total songs: {results['total_songs']}")
        print(f"Successfully processed: {results['processed_songs']}")
        print(f"Validation passed: {results['validation_passed']}")
        print(f"Validation failed: {results['validation_failed']}")
        print(f"Processing errors: {len(results['processing_errors'])}")
        print(f"Average BPM error: {results.get('avg_bpm_error', 0):.2f}%")
        print(f"Average tempo drift: {results.get('avg_tempo_drift', 0):.2f}%")
        print(f"Processing time: {results.get('processing_time_formatted', 'N/A')}")
        
        # Show validation rate
        if results['processed_songs'] > 0:
            validation_rate = (results['validation_passed'] / results['processed_songs']) * 100
            print(f"Validation pass rate: {validation_rate:.1f}%")
        
        # Show errors if any
        if results['processing_errors']:
            print(f"\nProcessing errors ({len(results['processing_errors'])}):")
            for error in results['processing_errors'][:5]:  # Show first 5 errors
                print(f"  - {error['song']}: {error['error']}")
            if len(results['processing_errors']) > 5:
                print(f"  ... and {len(results['processing_errors']) - 5} more")
        
        print("=" * 60)
        print("Phase 5 completed successfully!")
        print(f"Results saved to: {output_dir}")
        print("=" * 60)
        
        return 0
        
    except Exception as e:
        print(f"\nError running Phase 5: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
