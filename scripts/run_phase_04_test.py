"""
Test script to run Phase 4 on a small subset of data for validation.
"""

import sys
import logging
import time
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from phases.phase_04_beat_estimation import Phase4BeatEstimator


def main():
    """Run Phase 4 on a small test subset."""
    
    print("="*60)
    print("🎵 PHASE 4: BEAT ESTIMATION - TEST RUN")
    print("="*60)
    print("Testing on a small subset of segments...")
    print()
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Check if Phase 3 data exists
    phase3_dir = Path("data/processed/phase3")
    if not phase3_dir.exists():
        print("❌ Phase 3 data not found")
        return False
    
    # Find segment files
    segment_files = list((phase3_dir / "audio_segments").glob("*_segment_*.npy"))
    if not segment_files:
        print("❌ No segment files found")
        return False
    
    # Use only first 50 segments for testing
    test_segments = segment_files[:50]
    print(f"✅ Testing with {len(test_segments)} segments")
    
    # Configuration for test run
    config = {
        "sample_rate": 22050,
        "hop_length": 512,
        "detection_method": "librosa",  # Use only librosa for faster processing
        "use_tja_bpm": False,  # Skip TJA BPM lookup for test
        "onset_threshold": 0.3,
        "beat_confidence_threshold": 0.7,
        "tempo_confidence_threshold": 0.6,
        "max_beat_interval_variance": 0.3,
        "clustering_eps": 0.05,
        "visualization": False,  # Skip visualizations for test
        "batch_size": 10,
        "max_processing_time": 30,
        "memory_limit_mb": 1024
    }
    
    try:
        # Initialize Phase 4 processor
        estimator = Phase4BeatEstimator(config)
        
        # Create test output directory
        test_output_dir = Path("data/processed/phase4_test")
        test_output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup output directories
        output_subdirs = [
            "beat_positions", "onset_positions", "tempo_analysis", 
            "validation"
        ]
        
        for subdir in output_subdirs:
            (test_output_dir / subdir).mkdir(parents=True, exist_ok=True)
        
        # Process test segments
        start_time = time.time()
        
        success_count = 0
        failed_count = 0
        all_tempos = []
        all_beat_counts = []
        
        print("Processing test segments...")
        for i, segment_file in enumerate(test_segments):
            print(f"  Processing {i+1}/{len(test_segments)}: {segment_file.name}")
            
            result = estimator.process_single_segment(
                segment_file, 
                test_output_dir, 
                config["use_tja_bpm"]
            )
            
            if result["success"]:
                success_count += 1
                if result["tempo"] > 0:
                    all_tempos.append(result["tempo"])
                    all_beat_counts.append(result["beat_count"])
                print(f"    ✅ Success: {result['beat_count']} beats, {result['tempo']:.1f} BPM")
            else:
                failed_count += 1
                print(f"    ❌ Failed: {result.get('error', 'Unknown error')}")
        
        processing_time = time.time() - start_time
        
        # Display results
        print("\n" + "="*60)
        print("📊 TEST RESULTS")
        print("="*60)
        
        success_rate = success_count / len(test_segments) * 100
        
        print(f"Total segments: {len(test_segments)}")
        print(f"Successfully processed: {success_count}")
        print(f"Failed: {failed_count}")
        print(f"Success rate: {success_rate:.1f}%")
        print(f"Processing time: {processing_time:.1f} seconds")
        print(f"Average time per segment: {processing_time/len(test_segments):.2f} seconds")
        
        if all_tempos:
            import numpy as np
            print(f"Average tempo: {np.mean(all_tempos):.1f} BPM")
            print(f"Average beats per segment: {np.mean(all_beat_counts):.1f}")
            print(f"Tempo range: {np.min(all_tempos):.1f} - {np.max(all_tempos):.1f} BPM")
        
        print()
        print("📁 Generated Test Files:")
        
        beat_files = len(list((test_output_dir / "beat_positions").glob("*.json")))
        onset_files = len(list((test_output_dir / "onset_positions").glob("*.json")))
        tempo_files = len(list((test_output_dir / "tempo_analysis").glob("*.json")))
        validation_files = len(list((test_output_dir / "validation").glob("*.json")))
        
        print(f"  - Beat positions: {beat_files} files")
        print(f"  - Onset positions: {onset_files} files")
        print(f"  - Tempo analysis: {tempo_files} files")
        print(f"  - Validation results: {validation_files} files")
        
        # Check a sample output file
        if beat_files > 0:
            sample_file = list((test_output_dir / "beat_positions").glob("*.json"))[0]
            print(f"\nSample output file: {sample_file.name}")
            
            import json
            with open(sample_file, 'r') as f:
                sample_data = json.load(f)
            
            print(f"  - Beats detected: {len(sample_data.get('beats', []))}")
            print(f"  - Tempo: {sample_data.get('tempo', 0):.1f} BPM")
            print(f"  - Detection method: {sample_data.get('detection_method', 'unknown')}")
            print(f"  - Quality score: {sample_data.get('processing_metadata', {}).get('quality_score', 0):.3f}")
        
        print()
        
        if success_rate >= 80:
            print("✅ Test completed successfully! Phase 4 implementation is working correctly.")
            print("Ready to process the full dataset.")
        else:
            print("⚠️  Test completed with issues. Review the implementation before full processing.")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
