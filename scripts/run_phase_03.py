"""
Phase 3 Execution Script: Silence Detection & Audio Segmentation
Provides convenient interface for running Phase 3 processing with validation and monitoring.
"""

import os
import sys
import time
import argparse
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from phases.phase_03_silence_detection import Phase3SilenceProcessor

def print_banner():
    """Print Phase 3 banner."""
    print("="*80)
    print("🧩 PHASE 3: SILENCE DETECTION & AUDIO SEGMENTATION")
    print("="*80)
    print("Detecting silence regions and segmenting audio into musical sections")
    print("Input: Phase 2 filtered audio files (2,712 files)")
    print("Output: Silence maps, audio segments, and energy profiles")
    print("="*80)

def check_prerequisites():
    """Check if Phase 2 outputs are available."""
    required_paths = [
        "data/processed/phase2/filtered_audio",
        "data/processed/phase2/filtered_metadata",
        "data/processed/phase2/quality_report.json"
    ]

    missing_paths = []
    for path in required_paths:
        if not Path(path).exists():
            missing_paths.append(path)

    if missing_paths:
        print("❌ Prerequisites not met. Missing paths:")
        for path in missing_paths:
            print(f"   - {path}")
        print("\nPlease run Phase 2 first to generate required inputs.")
        return False

    # Check if we have audio files
    audio_files = list(Path("data/processed/phase2/filtered_audio").glob("*.npy"))
    if not audio_files:
        print("❌ No audio files found in Phase 2 outputs.")
        return False

    print(f"✅ Prerequisites met. Found {len(audio_files)} audio files from Phase 2.")

    # Clean up any existing Phase 3 outputs
    phase3_path = Path("data/processed/phase3")
    if phase3_path.exists():
        print("🧹 Cleaning up existing Phase 3 outputs...")
        import shutil
        shutil.rmtree(phase3_path)
        print("✅ Previous outputs cleaned")

    return True

def run_validation_mode():
    """Run Phase 3 in validation mode with subset of files."""
    print("\n🔍 RUNNING VALIDATION MODE")
    print("-" * 40)
    print("Processing 50 files to validate implementation...")
    
    try:
        processor = Phase3SilenceProcessor()
        results = processor.process_dataset(validation_mode=True)
        
        # Check if validation passed
        gates_status = processor._check_quality_gates()
        
        print(f"\n📊 Validation Results:")
        print(f"   Files processed: {results['processed_files']}")
        print(f"   Success rate: {gates_status['success_rate']:.1%}")
        print(f"   Quality pass rate: {gates_status['quality_pass_rate']:.1%}")
        print(f"   Total segments: {results['total_segments']}")
        print(f"   Avg silence %: {results['average_silence_percentage']:.1f}%")
        
        if gates_status['overall_passed']:
            print("✅ Validation PASSED - Ready for full dataset processing")
            return True
        else:
            print("❌ Validation FAILED - Please review configuration")
            return False
            
    except Exception as e:
        print(f"❌ Validation failed with error: {e}")
        return False

def run_full_processing():
    """Run Phase 3 on full dataset."""
    print("\n🚀 RUNNING FULL DATASET PROCESSING")
    print("-" * 40)
    print("Processing all 2,712 files from Phase 2...")
    print("This may take 30-60 minutes depending on system performance.")
    
    # Confirm with user
    response = input("\nProceed with full processing? (y/N): ").strip().lower()
    if response != 'y':
        print("Processing cancelled by user.")
        return False
    
    try:
        start_time = time.time()
        processor = Phase3SilenceProcessor()
        results = processor.process_dataset(validation_mode=False)
        total_time = time.time() - start_time
        
        # Print comprehensive results
        print(f"\n📊 PROCESSING COMPLETE")
        print(f"   Total time: {total_time/60:.1f} minutes")
        print(f"   Files processed: {results['processed_files']}")
        print(f"   Successful files: {results['successful_files']}")
        print(f"   Quality passed: {results['quality_passed_files']}")
        print(f"   Failed files: {results['failed_files']}")
        print(f"   Total segments: {results['total_segments']}")
        print(f"   Avg segments/file: {results['average_segments_per_file']:.1f}")
        print(f"   Avg silence %: {results['average_silence_percentage']:.1f}%")
        
        # Check quality gates
        gates_status = processor._check_quality_gates()
        if gates_status['overall_passed']:
            print("✅ PHASE 3 COMPLETED SUCCESSFULLY")
            print("Ready to proceed to Phase 4: Beat Position Estimation")
            return True
        else:
            print("❌ PHASE 3 FAILED QUALITY GATES")
            print("Please review results and configuration before proceeding")
            return False
            
    except Exception as e:
        print(f"❌ Full processing failed with error: {e}")
        return False

def show_output_summary():
    """Show summary of Phase 3 outputs."""
    output_paths = {
        "Silence Maps": "data/processed/phase3/silence_maps",
        "Audio Segments": "data/processed/phase3/audio_segments", 
        "Energy Profiles": "data/processed/phase3/energy_profiles",
        "Processing Report": "data/processed/phase3/segmentation_report.json"
    }
    
    print(f"\n📁 PHASE 3 OUTPUTS:")
    for name, path in output_paths.items():
        if Path(path).exists():
            if Path(path).is_dir():
                file_count = len(list(Path(path).glob("*")))
                print(f"   ✅ {name}: {file_count} files in {path}")
            else:
                print(f"   ✅ {name}: {path}")
        else:
            print(f"   ❌ {name}: {path} (not found)")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(
        description="Phase 3: Silence Detection & Audio Segmentation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/run_phase_03.py --validate    # Run validation mode first
  python scripts/run_phase_03.py --full        # Run full processing
  python scripts/run_phase_03.py --auto        # Auto mode (validate then full)
  python scripts/run_phase_03.py --status      # Show current status
        """
    )
    
    parser.add_argument("--validate", action="store_true", 
                       help="Run validation mode with 50 files")
    parser.add_argument("--full", action="store_true", 
                       help="Run full dataset processing")
    parser.add_argument("--auto", action="store_true", 
                       help="Auto mode: validate then full processing")
    parser.add_argument("--status", action="store_true", 
                       help="Show current Phase 3 status")
    parser.add_argument("--config", default="configs/phase_03_config.yaml",
                       help="Configuration file path")
    
    args = parser.parse_args()
    
    print_banner()
    
    # Show status if requested
    if args.status:
        show_output_summary()
        return 0
    
    # Check prerequisites
    if not check_prerequisites():
        return 1
    
    success = False
    
    if args.validate:
        success = run_validation_mode()
    elif args.full:
        success = run_full_processing()
    elif args.auto:
        print("🔄 AUTO MODE: Running validation first...")
        if run_validation_mode():
            print("\n🔄 Validation passed, proceeding to full processing...")
            success = run_full_processing()
        else:
            print("❌ Validation failed, stopping auto mode")
            success = False
    else:
        print("Please specify --validate, --full, --auto, or --status")
        print("Use --help for more information")
        return 1
    
    # Show final outputs
    show_output_summary()
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
