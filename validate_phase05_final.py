#!/usr/bin/env python3
"""
Final validation script for Phase 5 production readiness.
"""

import json
from pathlib import Path
import numpy as np

def validate_output_structure():
    """Validate output directory structure matches documentation."""
    print("🔍 Validating output structure...")
    
    base_dir = Path("data/processed/phase5")
    expected_dirs = ["tempo_alignment", "bpm_validation", "aligned_beats", "timing_analysis"]
    
    results = {}
    for dir_name in expected_dirs:
        dir_path = base_dir / dir_name
        if dir_path.exists():
            file_count = len(list(dir_path.glob("*.json")))
            results[dir_name] = file_count
            print(f"  ✅ {dir_name}: {file_count} files")
        else:
            results[dir_name] = 0
            print(f"  ❌ {dir_name}: Missing")
    
    # Check alignment report
    report_file = base_dir / "alignment_report.json"
    if report_file.exists():
        print(f"  ✅ alignment_report.json: Present")
        results["alignment_report"] = True
    else:
        print(f"  ❌ alignment_report.json: Missing")
        results["alignment_report"] = False
    
    return results

def validate_json_format_consistency():
    """Validate JSON format consistency across output files."""
    print("\n🔍 Validating JSON format consistency...")
    
    base_dir = Path("data/processed/phase5")
    
    # Sample files from each directory
    sample_files = {
        "tempo_alignment": list((base_dir / "tempo_alignment").glob("*.json"))[:3],
        "bpm_validation": list((base_dir / "bpm_validation").glob("*.json"))[:3],
        "aligned_beats": list((base_dir / "aligned_beats").glob("*.json"))[:3],
        "timing_analysis": list((base_dir / "timing_analysis").glob("*.json"))[:3]
    }
    
    format_issues = []
    
    for dir_name, files in sample_files.items():
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Validate required fields based on documentation
                if dir_name == "bpm_validation":
                    required_fields = ["tja_bpm", "detected_bpm", "bpm_error_percentage", "validation_passed"]
                    for field in required_fields:
                        if field not in data:
                            format_issues.append(f"{file_path.name}: Missing {field}")
                
                elif dir_name == "tempo_alignment":
                    required_fields = ["aligned_bpm", "bpm_confidence", "tempo_drift", "beat_grid"]
                    for field in required_fields:
                        if field not in data:
                            format_issues.append(f"{file_path.name}: Missing {field}")
                
            except json.JSONDecodeError as e:
                format_issues.append(f"{file_path.name}: Invalid JSON - {e}")
            except Exception as e:
                format_issues.append(f"{file_path.name}: Error - {e}")
    
    if format_issues:
        print(f"  ❌ Found {len(format_issues)} format issues:")
        for issue in format_issues[:5]:  # Show first 5
            print(f"    - {issue}")
    else:
        print("  ✅ All sampled files have consistent JSON format")
    
    return len(format_issues) == 0

def validate_performance_metrics():
    """Validate performance metrics are within acceptable ranges."""
    print("\n🔍 Validating performance metrics...")
    
    # Load alignment report
    report_file = Path("data/processed/phase5/alignment_report.json")
    if not report_file.exists():
        print("  ❌ Alignment report not found")
        return False
    
    with open(report_file, 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    # Check key metrics
    metrics = report.get("processing_summary", {})
    performance = report.get("performance_metrics", {})
    
    pass_rate = metrics.get("pass_rate_percentage", 0)
    avg_error = performance.get("average_bpm_error_percentage", 100)
    
    print(f"  📊 Pass Rate: {pass_rate}%")
    print(f"  📊 Average BPM Error: {avg_error}%")
    
    # Validate against targets
    issues = []
    if pass_rate < 60:
        issues.append(f"Pass rate {pass_rate}% below target 60%")
    if avg_error > 10:
        issues.append(f"Average BPM error {avg_error}% above target 10%")
    
    if issues:
        print("  ❌ Performance issues:")
        for issue in issues:
            print(f"    - {issue}")
        return False
    else:
        print("  ✅ All performance metrics within acceptable ranges")
        return True

def validate_phase6_compatibility():
    """Validate output format compatibility with Phase 6 requirements."""
    print("\n🔍 Validating Phase 6 compatibility...")
    
    # Check aligned_beats format (critical for Phase 6)
    beats_dir = Path("data/processed/phase5/aligned_beats")
    sample_files = list(beats_dir.glob("*.json"))[:3]
    
    compatibility_issues = []
    
    for file_path in sample_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                beats_data = json.load(f)
            
            if isinstance(beats_data, list) and len(beats_data) > 0:
                # Check first beat has required fields for Phase 6
                first_beat = beats_data[0]
                required_fields = ["beat_time", "confidence", "beat_id"]
                
                for field in required_fields:
                    if field not in first_beat:
                        compatibility_issues.append(f"{file_path.name}: Missing {field} in beat data")
            else:
                compatibility_issues.append(f"{file_path.name}: Invalid beats data structure")
                
        except Exception as e:
            compatibility_issues.append(f"{file_path.name}: Error reading beats - {e}")
    
    if compatibility_issues:
        print(f"  ❌ Found {len(compatibility_issues)} compatibility issues:")
        for issue in compatibility_issues:
            print(f"    - {issue}")
        return False
    else:
        print("  ✅ Output format compatible with Phase 6 requirements")
        return True

def validate_logging_consistency():
    """Validate logging files are created and consistent."""
    print("\n🔍 Validating logging consistency...")
    
    log_dir = Path("data/logs")
    phase5_logs = list(log_dir.glob("phase05_*.log"))
    
    if not phase5_logs:
        print("  ❌ No Phase 5 log files found")
        return False
    
    # Check most recent log
    latest_log = max(phase5_logs, key=lambda x: x.stat().st_mtime)
    
    try:
        with open(latest_log, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # Check for key log messages
        required_messages = [
            "Phase 5 Tempo Alignment Processor initialized",
            "Processing complete"
        ]
        
        missing_messages = []
        for message in required_messages:
            if message not in log_content:
                missing_messages.append(message)
        
        if missing_messages:
            print(f"  ❌ Missing log messages: {missing_messages}")
            return False
        else:
            print(f"  ✅ Log file {latest_log.name} contains expected messages")
            return True
            
    except Exception as e:
        print(f"  ❌ Error reading log file: {e}")
        return False

def main():
    """Run comprehensive final validation."""
    print("=" * 60)
    print("Phase 5 Final Production Readiness Validation")
    print("=" * 60)
    
    validations = [
        ("Output Structure", validate_output_structure),
        ("JSON Format Consistency", validate_json_format_consistency),
        ("Performance Metrics", validate_performance_metrics),
        ("Phase 6 Compatibility", validate_phase6_compatibility),
        ("Logging Consistency", validate_logging_consistency)
    ]
    
    results = {}
    for name, validation_func in validations:
        try:
            if name == "Output Structure":
                result = validation_func()
                # For output structure, check if all directories have files
                results[name] = all(count > 0 for count in result.values() if isinstance(count, int))
            else:
                results[name] = validation_func()
        except Exception as e:
            print(f"  ❌ Validation {name} failed with exception: {e}")
            results[name] = False
    
    print("\n" + "=" * 60)
    print("Final Validation Results")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} validations passed")
    
    if passed == total:
        print("\n🎉 Phase 5 is PRODUCTION READY!")
        print("✅ All validations passed")
        print("✅ Output structure matches documentation")
        print("✅ Performance metrics exceed targets")
        print("✅ Format compatible with Phase 6")
        print("✅ Logging is consistent and complete")
        return 0
    else:
        print(f"\n⚠️  Phase 5 needs attention: {total-passed} validation(s) failed")
        return 1

if __name__ == "__main__":
    exit(main())
